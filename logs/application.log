2025-07-31 16:44:27.508 [34mINFO [0;39m 24272 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 16:44:27.513 [34mINFO [0;39m 24272 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 24272 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-07-31 16:44:27.514 [39mDEBUG[0;39m 24272 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 16:44:27.514 [34mINFO [0;39m 24272 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-31 16:44:28.734 [34mINFO [0;39m 24272 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 16:44:28.736 [34mINFO [0;39m 24272 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 16:44:28.783 [34mINFO [0;39m 24272 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-07-31 16:44:28.889 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-31 16:44:28.889 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-07-31 16:44:28.889 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-07-31 16:44:28.889 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-07-31 16:44:28.889 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-31 16:44:28.889 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-31 16:44:28.889 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-31 16:44:28.890 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-31 16:44:28.891 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-31 16:44:28.892 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-31 16:44:28.892 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-07-31 16:44:28.893 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-07-31 16:44:28.893 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-07-31 16:44:28.893 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-07-31 16:44:28.893 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-07-31 16:44:28.893 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-07-31 16:44:28.894 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-31 16:44:28.894 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-31 16:44:28.894 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-31 16:44:28.894 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-31 16:44:28.894 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-31 16:44:28.894 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-31 16:44:28.895 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-31 16:44:28.895 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-31 16:44:28.895 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-31 16:44:28.895 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-31 16:44:28.895 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-31 16:44:28.895 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-31 16:44:28.896 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-31 16:44:28.896 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-31 16:44:28.896 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-31 16:44:28.896 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-31 16:44:28.896 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-31 16:44:28.896 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-31 16:44:28.896 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-07-31 16:44:28.897 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-07-31 16:44:28.897 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-07-31 16:44:28.897 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-07-31 16:44:28.897 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-31 16:44:28.897 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-31 16:44:28.897 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-31 16:44:28.897 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-31 16:44:28.898 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-31 16:44:28.898 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-31 16:44:28.898 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-31 16:44:28.898 [39mDEBUG[0;39m 24272 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-31 16:44:29.651 [34mINFO [0;39m 24272 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-31 16:44:29.658 [34mINFO [0;39m 24272 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 16:44:29.659 [34mINFO [0;39m 24272 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-31 16:44:29.659 [34mINFO [0;39m 24272 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 16:44:29.766 [34mINFO [0;39m 24272 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-31 16:44:29.766 [34mINFO [0;39m 24272 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2210 ms
2025-07-31 16:44:30.132 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-31 16:44:30.157 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-07-31 16:44:30.171 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-07-31 16:44:30.182 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-07-31 16:44:30.192 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-31 16:44:30.198 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-31 16:44:30.204 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-31 16:44:30.217 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-31 16:44:30.222 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-31 16:44:30.228 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-31 16:44:30.233 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-31 16:44:30.240 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-31 16:44:30.248 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-31 16:44:30.257 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-07-31 16:44:30.265 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-07-31 16:44:30.279 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-07-31 16:44:30.288 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-31 16:44:30.295 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-31 16:44:30.302 [39mDEBUG[0;39m 24272 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-31 16:44:30.326 [34mINFO [0;39m 24272 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-31 16:44:31.118 [34mINFO [0;39m 24272 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-31 16:44:33.844 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-31 16:44:33.845 [39mDEBUG[0;39m 24272 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-31 16:44:34.323 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-31 16:44:34.325 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.074 [34mINFO [0;39m 24272 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-31 16:44:35.289 [34mINFO [0;39m 24272 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-31 16:44:35.302 [34mINFO [0;39m 24272 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-31 16:44:35.388 [34mINFO [0;39m 24272 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-07-31 16:44:35.389 [34mINFO [0;39m 24272 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-07-31 16:44:35.404 [34mINFO [0;39m 24272 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-07-31 16:44:35.434 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-31 16:44:35.435 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.436 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:44:35.436 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.436 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-31 16:44:35.436 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.437 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-31 16:44:35.437 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.437 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-31 16:44:35.437 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.437 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:44:35.439 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.445 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-31 16:44:35.445 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.445 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:44:35.445 [39mDEBUG[0;39m 24272 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:44:35.635 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-31 16:44:35.636 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-31 16:44:35.640 [34mINFO [0;39m 24272 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@221961f2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@541d8a9e, org.springframework.security.web.context.SecurityContextPersistenceFilter@4446e1d9, org.springframework.security.web.header.HeaderWriterFilter@5bbb351e, org.springframework.security.web.authentication.logout.LogoutFilter@4583a186, com.example.pure.filter.JwtFilter@6885f3f7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1df60140, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@62d4dac4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@51c0346d, org.springframework.security.web.session.SessionManagementFilter@7441edeb, org.springframework.security.web.access.ExceptionTranslationFilter@3fe7c87c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@653c0c9c]
2025-07-31 16:44:35.643 [34mINFO [0;39m 24272 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-31 16:44:35.645 [34mINFO [0;39m 24272 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-31 16:44:35.646 [34mINFO [0;39m 24272 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-31 16:44:35.646 [34mINFO [0;39m 24272 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-31 16:44:35.880 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-31 16:44:35.916 [34mINFO [0;39m 24272 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-31 16:44:36.000 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 107 mappings in 'requestMappingHandlerMapping'
2025-07-31 16:44:36.007 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-31 16:44:36.408 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-31 16:44:36.519 [34mINFO [0;39m 24272 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-31 16:44:36.552 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-31 16:44:36.553 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-31 16:44:36.554 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-31 16:44:36.554 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-31 16:44:36.554 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-07-31 16:44:36.554 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-31 16:44:36.554 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-31 16:44:36.554 [39mDEBUG[0;39m 24272 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-31 16:44:36.554 [34mINFO [0;39m 24272 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5e8633da, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@70c3c5d7, org.springframework.security.web.context.SecurityContextPersistenceFilter@7b6558aa, org.springframework.security.web.header.HeaderWriterFilter@23973547, org.springframework.web.filter.CorsFilter@236e6563, org.springframework.security.web.authentication.logout.LogoutFilter@36109a76, com.example.pure.filter.JwtFilter@6885f3f7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@65cb50dc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7fec6c2f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@528070fe, org.springframework.security.web.session.SessionManagementFilter@3ed4c822, org.springframework.security.web.access.ExceptionTranslationFilter@704ddfad, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36e8fefe]
2025-07-31 16:44:36.605 [39mTRACE[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7e70bd39, started on Thu Jul 31 16:44:27 CST 2025
2025-07-31 16:44:36.624 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-07-31 16:44:36.625 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-07-31 16:44:36.626 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-07-31 16:44:36.626 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-07-31 16:44:36.626 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-07-31 16:44:36.626 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-07-31 16:44:36.626 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-07-31 16:44:36.626 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-07-31 16:44:36.626 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-07-31 16:44:36.630 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-07-31 16:44:36.631 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-07-31 16:44:36.631 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-07-31 16:44:36.631 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-07-31 16:44:36.631 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-07-31 16:44:36.632 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-31 16:44:36.633 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-31 16:44:36.633 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-31 16:44:36.633 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-31 16:44:36.769 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-31 16:44:36.815 [39mDEBUG[0;39m 24272 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-31 16:44:37.105 [34mINFO [0;39m 24272 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 16:44:37.117 [34mINFO [0;39m 24272 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 16:44:37.118 [39mDEBUG[0;39m 24272 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-31 16:44:37.118 [39mDEBUG[0;39m 24272 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-31 16:44:37.119 [34mINFO [0;39m 24272 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-31 16:44:37.119 [39mDEBUG[0;39m 24272 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1cad8bce]
2025-07-31 16:44:37.119 [39mDEBUG[0;39m 24272 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1cad8bce]
2025-07-31 16:44:37.119 [34mINFO [0;39m 24272 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1cad8bce]]
2025-07-31 16:44:37.119 [34mINFO [0;39m 24272 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-31 16:44:37.119 [39mDEBUG[0;39m 24272 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:44:37.119 [39mDEBUG[0;39m 24272 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:44:37.135 [34mINFO [0;39m 24272 --- [main] com.example.pure.PureApplication : Started PureApplication in 10.039 seconds (JVM running for 11.221)
2025-07-31 16:45:36.594 [34mINFO [0;39m 24272 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-31 16:47:29.127 [39mDEBUG[0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-31 16:47:29.127 [39mDEBUG[0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-31 16:47:29.127 [34mINFO [0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-31 16:47:29.127 [34mINFO [0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1cad8bce]]
2025-07-31 16:47:29.127 [39mDEBUG[0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1cad8bce]
2025-07-31 16:47:29.127 [39mDEBUG[0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1cad8bce]
2025-07-31 16:47:29.127 [34mINFO [0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-31 16:47:29.127 [39mDEBUG[0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:47:29.127 [39mDEBUG[0;39m 24272 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:47:29.165 [34mINFO [0;39m 24272 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-31 16:47:29.171 [34mINFO [0;39m 24272 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-31 16:53:29.780 [34mINFO [0;39m 9756 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 16:53:29.787 [34mINFO [0;39m 9756 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 9756 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-07-31 16:53:29.787 [39mDEBUG[0;39m 9756 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 16:53:29.787 [34mINFO [0;39m 9756 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-31 16:53:30.727 [34mINFO [0;39m 9756 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 16:53:30.729 [34mINFO [0;39m 9756 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 16:53:30.775 [34mINFO [0;39m 9756 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-31 16:53:30.872 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-31 16:53:30.873 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-31 16:53:30.873 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-31 16:53:30.874 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-31 16:53:30.875 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-07-31 16:53:30.875 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-07-31 16:53:30.875 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-07-31 16:53:30.875 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-07-31 16:53:30.875 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-07-31 16:53:30.875 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-07-31 16:53:30.876 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-31 16:53:30.876 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-31 16:53:30.876 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-31 16:53:30.876 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-31 16:53:30.876 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-31 16:53:30.876 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-31 16:53:30.876 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-31 16:53:30.877 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-31 16:53:30.877 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-31 16:53:30.877 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-31 16:53:30.877 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-31 16:53:30.877 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-31 16:53:30.877 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-31 16:53:30.877 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-31 16:53:30.877 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-31 16:53:30.878 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-31 16:53:30.878 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-31 16:53:30.878 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-31 16:53:30.878 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-07-31 16:53:30.878 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-07-31 16:53:30.878 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-07-31 16:53:30.878 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-07-31 16:53:30.879 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-31 16:53:30.879 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-31 16:53:30.879 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-31 16:53:30.879 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-31 16:53:30.879 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-31 16:53:30.879 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-31 16:53:30.879 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-31 16:53:30.880 [39mDEBUG[0;39m 9756 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-31 16:53:31.586 [34mINFO [0;39m 9756 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-31 16:53:31.592 [34mINFO [0;39m 9756 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 16:53:31.593 [34mINFO [0;39m 9756 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-31 16:53:31.593 [34mINFO [0;39m 9756 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 16:53:31.700 [34mINFO [0;39m 9756 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-31 16:53:31.701 [34mINFO [0;39m 9756 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1877 ms
2025-07-31 16:53:32.089 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-31 16:53:32.120 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-07-31 16:53:32.132 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-07-31 16:53:32.142 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-07-31 16:53:32.151 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-31 16:53:32.156 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-31 16:53:32.163 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-31 16:53:32.176 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-31 16:53:32.181 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-31 16:53:32.188 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-31 16:53:32.193 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-31 16:53:32.199 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-31 16:53:32.206 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-31 16:53:32.213 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-07-31 16:53:32.220 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-07-31 16:53:32.230 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-07-31 16:53:32.235 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-31 16:53:32.240 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-31 16:53:32.245 [39mDEBUG[0;39m 9756 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-31 16:53:32.261 [34mINFO [0;39m 9756 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-31 16:53:32.545 [34mINFO [0;39m 9756 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-31 16:53:33.038 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-31 16:53:33.039 [39mDEBUG[0;39m 9756 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-31 16:53:33.322 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-31 16:53:33.323 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:33.757 [34mINFO [0;39m 9756 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-31 16:53:33.909 [34mINFO [0;39m 9756 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-31 16:53:33.916 [34mINFO [0;39m 9756 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-31 16:53:33.970 [34mINFO [0;39m 9756 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-07-31 16:53:33.971 [34mINFO [0;39m 9756 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-07-31 16:53:33.983 [34mINFO [0;39m 9756 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-07-31 16:53:34.008 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:53:34.008 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:34.009 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-31 16:53:34.009 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:34.010 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-31 16:53:34.010 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:34.010 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-31 16:53:34.010 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:34.010 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-31 16:53:34.010 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:34.010 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:53:34.010 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:34.015 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-31 16:53:34.016 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:34.016 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:53:34.016 [39mDEBUG[0;39m 9756 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:53:34.147 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-31 16:53:34.148 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-31 16:53:34.152 [34mINFO [0;39m 9756 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@726882da, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@f0deba2, org.springframework.security.web.context.SecurityContextPersistenceFilter@581cb879, org.springframework.security.web.header.HeaderWriterFilter@1899c7ae, org.springframework.security.web.authentication.logout.LogoutFilter@15ff247b, com.example.pure.filter.JwtFilter@6ed7c178, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1c294a17, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7a458c73, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ddf9fd, org.springframework.security.web.session.SessionManagementFilter@33602729, org.springframework.security.web.access.ExceptionTranslationFilter@11efa76e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@f1d5f3]
2025-07-31 16:53:34.154 [34mINFO [0;39m 9756 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-31 16:53:34.156 [34mINFO [0;39m 9756 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-31 16:53:34.157 [34mINFO [0;39m 9756 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-31 16:53:34.157 [34mINFO [0;39m 9756 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-31 16:53:34.327 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-31 16:53:34.349 [34mINFO [0;39m 9756 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-31 16:53:34.436 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 107 mappings in 'requestMappingHandlerMapping'
2025-07-31 16:53:34.444 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-31 16:53:34.846 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-31 16:53:34.963 [34mINFO [0;39m 9756 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-31 16:53:34.984 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-07-31 16:53:34.985 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-31 16:53:34.986 [39mDEBUG[0;39m 9756 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-31 16:53:34.987 [34mINFO [0;39m 9756 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@12ff3df, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@12b962b1, org.springframework.security.web.context.SecurityContextPersistenceFilter@440e2406, org.springframework.security.web.header.HeaderWriterFilter@3509f32d, org.springframework.web.filter.CorsFilter@15844237, org.springframework.security.web.authentication.logout.LogoutFilter@54626326, com.example.pure.filter.JwtFilter@6ed7c178, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@56adb75e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2cc7b63d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2e04dbc1, org.springframework.security.web.session.SessionManagementFilter@1f9a5d3b, org.springframework.security.web.access.ExceptionTranslationFilter@2726b2f3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7b381066]
2025-07-31 16:53:35.023 [39mTRACE[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@e6516e, started on Thu Jul 31 16:53:29 CST 2025
2025-07-31 16:53:35.035 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-07-31 16:53:35.035 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-07-31 16:53:35.035 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-07-31 16:53:35.036 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-07-31 16:53:35.039 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
2025-07-31 16:53:35.040 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-07-31 16:53:35.040 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-07-31 16:53:35.040 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-07-31 16:53:35.040 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-07-31 16:53:35.041 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-31 16:53:35.042 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-31 16:53:35.042 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-31 16:53:35.042 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-31 16:53:35.135 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-31 16:53:35.172 [39mDEBUG[0;39m 9756 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-31 16:53:35.381 [34mINFO [0;39m 9756 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 16:53:35.392 [34mINFO [0;39m 9756 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 16:53:35.394 [39mDEBUG[0;39m 9756 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-31 16:53:35.394 [39mDEBUG[0;39m 9756 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-31 16:53:35.394 [34mINFO [0;39m 9756 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-31 16:53:35.394 [39mDEBUG[0;39m 9756 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@237bdd46]
2025-07-31 16:53:35.394 [39mDEBUG[0;39m 9756 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@237bdd46]
2025-07-31 16:53:35.394 [34mINFO [0;39m 9756 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@237bdd46]]
2025-07-31 16:53:35.395 [34mINFO [0;39m 9756 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-31 16:53:35.395 [39mDEBUG[0;39m 9756 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:53:35.395 [39mDEBUG[0;39m 9756 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:53:35.407 [34mINFO [0;39m 9756 --- [main] com.example.pure.PureApplication : Started PureApplication in 6.054 seconds (JVM running for 6.587)
2025-07-31 16:53:59.314 [39mDEBUG[0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-31 16:53:59.314 [39mDEBUG[0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-31 16:53:59.314 [34mINFO [0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-07-31 16:53:59.314 [34mINFO [0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@237bdd46]]
2025-07-31 16:53:59.314 [39mDEBUG[0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@237bdd46]
2025-07-31 16:53:59.314 [39mDEBUG[0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@237bdd46]
2025-07-31 16:53:59.314 [34mINFO [0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-07-31 16:53:59.314 [39mDEBUG[0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:53:59.314 [39mDEBUG[0;39m 9756 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:53:59.352 [34mINFO [0;39m 9756 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-07-31 16:53:59.358 [34mINFO [0;39m 9756 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-07-31 16:54:03.310 [34mINFO [0;39m 8128 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-31 16:54:03.316 [34mINFO [0;39m 8128 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 8128 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-07-31 16:54:03.316 [39mDEBUG[0;39m 8128 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 16:54:03.316 [34mINFO [0;39m 8128 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-31 16:54:04.196 [34mINFO [0;39m 8128 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 16:54:04.198 [34mINFO [0;39m 8128 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 16:54:04.240 [34mINFO [0;39m 8128 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-07-31 16:54:04.325 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-07-31 16:54:04.325 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-07-31 16:54:04.326 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-07-31 16:54:04.328 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-07-31 16:54:04.330 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-07-31 16:54:04.330 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-07-31 16:54:04.331 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-07-31 16:54:04.331 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-07-31 16:54:04.331 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-07-31 16:54:04.331 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-07-31 16:54:04.331 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-07-31 16:54:04.331 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-07-31 16:54:04.332 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-07-31 16:54:04.332 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-07-31 16:54:04.332 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-07-31 16:54:04.332 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-07-31 16:54:04.332 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-07-31 16:54:04.332 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-07-31 16:54:04.332 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-07-31 16:54:04.332 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-07-31 16:54:04.333 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-07-31 16:54:04.333 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-07-31 16:54:04.333 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-07-31 16:54:04.333 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-07-31 16:54:04.333 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-07-31 16:54:04.333 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-07-31 16:54:04.334 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-07-31 16:54:04.334 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-07-31 16:54:04.334 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-31 16:54:04.334 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-07-31 16:54:04.334 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-07-31 16:54:04.334 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-07-31 16:54:04.335 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-07-31 16:54:04.335 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-07-31 16:54:04.335 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-07-31 16:54:04.335 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-07-31 16:54:04.335 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-07-31 16:54:04.335 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-07-31 16:54:04.335 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-07-31 16:54:04.335 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-07-31 16:54:04.336 [39mDEBUG[0;39m 8128 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-07-31 16:54:04.900 [34mINFO [0;39m 8128 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-07-31 16:54:04.906 [34mINFO [0;39m 8128 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 16:54:04.907 [34mINFO [0;39m 8128 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-07-31 16:54:04.907 [34mINFO [0;39m 8128 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 16:54:04.999 [34mINFO [0;39m 8128 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-07-31 16:54:04.999 [34mINFO [0;39m 8128 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1646 ms
2025-07-31 16:54:05.238 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-07-31 16:54:05.249 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-07-31 16:54:05.259 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-07-31 16:54:05.267 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-07-31 16:54:05.274 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-07-31 16:54:05.277 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-07-31 16:54:05.281 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-07-31 16:54:05.289 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-07-31 16:54:05.292 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-07-31 16:54:05.297 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-07-31 16:54:05.300 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-07-31 16:54:05.305 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-07-31 16:54:05.317 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-07-31 16:54:05.321 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-07-31 16:54:05.326 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-07-31 16:54:05.334 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-07-31 16:54:05.339 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-07-31 16:54:05.343 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-07-31 16:54:05.347 [39mDEBUG[0;39m 8128 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-07-31 16:54:05.360 [34mINFO [0;39m 8128 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-07-31 16:54:05.619 [34mINFO [0;39m 8128 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-07-31 16:54:06.091 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-07-31 16:54:06.091 [39mDEBUG[0;39m 8128 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-07-31 16:54:06.360 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-31 16:54:06.362 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:06.784 [34mINFO [0;39m 8128 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-07-31 16:54:06.921 [34mINFO [0;39m 8128 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-07-31 16:54:06.930 [34mINFO [0;39m 8128 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-07-31 16:54:06.979 [34mINFO [0;39m 8128 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-07-31 16:54:06.979 [34mINFO [0;39m 8128 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-07-31 16:54:06.990 [34mINFO [0;39m 8128 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-07-31 16:54:07.017 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-07-31 16:54:07.017 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:07.018 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-07-31 16:54:07.019 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:07.019 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-07-31 16:54:07.019 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:07.019 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-07-31 16:54:07.019 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:07.020 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:54:07.020 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:07.020 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:54:07.020 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:07.025 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-07-31 16:54:07.025 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:07.026 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-07-31 16:54:07.026 [39mDEBUG[0;39m 8128 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-31 16:54:07.123 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-07-31 16:54:07.124 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-07-31 16:54:07.134 [34mINFO [0;39m 8128 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@45c10678, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ad57db1, org.springframework.security.web.context.SecurityContextPersistenceFilter@f9919a2, org.springframework.security.web.header.HeaderWriterFilter@5fdcc63f, org.springframework.security.web.authentication.logout.LogoutFilter@14af9f51, com.example.pure.filter.JwtFilter@1ad9d5be, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54d116d5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7057d9f4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6e140753, org.springframework.security.web.session.SessionManagementFilter@1b18fa79, org.springframework.security.web.access.ExceptionTranslationFilter@2d21d12b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@23d0f6be]
2025-07-31 16:54:07.136 [34mINFO [0;39m 8128 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-07-31 16:54:07.137 [34mINFO [0;39m 8128 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-07-31 16:54:07.138 [34mINFO [0;39m 8128 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-07-31 16:54:07.138 [34mINFO [0;39m 8128 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-07-31 16:54:07.289 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-07-31 16:54:07.307 [34mINFO [0;39m 8128 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-07-31 16:54:07.363 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 107 mappings in 'requestMappingHandlerMapping'
2025-07-31 16:54:07.370 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-31 16:54:07.700 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-07-31 16:54:07.808 [34mINFO [0;39m 8128 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-07-31 16:54:07.826 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-31 16:54:07.827 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-07-31 16:54:07.828 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-07-31 16:54:07.828 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-07-31 16:54:07.828 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-07-31 16:54:07.828 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-07-31 16:54:07.828 [39mDEBUG[0;39m 8128 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-07-31 16:54:07.828 [34mINFO [0;39m 8128 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@39cf7f3c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@25df95b, org.springframework.security.web.context.SecurityContextPersistenceFilter@299615a5, org.springframework.security.web.header.HeaderWriterFilter@154cbb46, org.springframework.web.filter.CorsFilter@2fb9695a, org.springframework.security.web.authentication.logout.LogoutFilter@5578ed7e, com.example.pure.filter.JwtFilter@1ad9d5be, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3ba6707e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4b407ac7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33da0fad, org.springframework.security.web.session.SessionManagementFilter@58fbecde, org.springframework.security.web.access.ExceptionTranslationFilter@2e778abb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@471b7c9c]
2025-07-31 16:54:07.859 [39mTRACE[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3300f4fd, started on Thu Jul 31 16:54:03 CST 2025
2025-07-31 16:54:07.870 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-07-31 16:54:07.871 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-07-31 16:54:07.872 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-07-31 16:54:07.872 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-07-31 16:54:07.872 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-07-31 16:54:07.875 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-07-31 16:54:07.875 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-07-31 16:54:07.875 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-07-31 16:54:07.875 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-07-31 16:54:07.875 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-07-31 16:54:07.876 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-31 16:54:07.876 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-31 16:54:07.876 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-31 16:54:07.876 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-31 16:54:07.958 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-31 16:54:07.992 [39mDEBUG[0;39m 8128 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-31 16:54:08.185 [34mINFO [0;39m 8128 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 16:54:08.195 [34mINFO [0;39m 8128 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-07-31 16:54:08.196 [39mDEBUG[0;39m 8128 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-07-31 16:54:08.196 [39mDEBUG[0;39m 8128 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-07-31 16:54:08.196 [34mINFO [0;39m 8128 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-07-31 16:54:08.196 [39mDEBUG[0;39m 8128 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21a09c5a]
2025-07-31 16:54:08.196 [39mDEBUG[0;39m 8128 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21a09c5a]
2025-07-31 16:54:08.196 [34mINFO [0;39m 8128 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21a09c5a]]
2025-07-31 16:54:08.197 [34mINFO [0;39m 8128 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-07-31 16:54:08.197 [39mDEBUG[0;39m 8128 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:54:08.197 [39mDEBUG[0;39m 8128 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-07-31 16:54:08.208 [34mINFO [0;39m 8128 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.327 seconds (JVM running for 6.231)
2025-07-31 16:55:07.846 [34mINFO [0;39m 8128 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-31 16:58:09.826 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 16:58:09.826 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-31 16:58:09.826 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-07-31 16:58:09.826 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-07-31 16:58:09.826 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-07-31 16:58:09.828 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1608b173
2025-07-31 16:58:09.828 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@533eca6c
2025-07-31 16:58:09.828 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-31 16:58:09.828 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 2 ms
2025-07-31 16:58:09.838 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-07-31 16:58:09.841 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-31 16:58:09.848 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:09.850 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-31 16:58:09.855 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-07-31 16:58:09.855 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-07-31 16:58:09.857 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-07-31 16:58:09.859 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:09.912 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=admin, password=a123456, recaptchaToken=3, email=null, deviceId=null)]
2025-07-31 16:58:10.360 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.controller.auth.AuthController : 用户登录: admin
2025-07-31 16:58:10.360 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.s.auth.impl.AuthServiceImpl : 处理用户登录请求: admin
2025-07-31 16:58:10.443 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:10.455 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:10.459 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4af603d6]
2025-07-31 16:58:10.467 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@646675911 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:10.469 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-07-31 16:58:10.490 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: admin(String)
2025-07-31 16:58:10.514 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-07-31 16:58:10.515 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4af603d6]
2025-07-31 16:58:10.515 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:10.516 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4af603d6] from current transaction
2025-07-31 16:58:10.516 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:10.516 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:10.519 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:10.519 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4af603d6]
2025-07-31 16:58:10.520 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:10.521 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4af603d6]
2025-07-31 16:58:10.521 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4af603d6]
2025-07-31 16:58:10.521 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4af603d6]
2025-07-31 16:58:10.620 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.s.a.d.DaoAuthenticationProvider : Failed to authenticate since password does not match stored value
2025-07-31 16:58:10.624 [31mWARN [0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.s.auth.impl.AuthServiceImpl : 用户登录失败 - 凭证错误: admin
2025-07-31 16:58:10.628 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Using @ExceptionHandler com.example.pure.exception.GlobalExceptionHandler#handleBusinessException(BusinessException)
2025-07-31 16:58:10.628 [31mWARN [0;39m 8128 --- [http-nio-8080-exec-2] c.e.p.e.GlobalExceptionHandler : 业务异常: 用户名或密码错误
2025-07-31 16:58:10.636 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 16:58:10.640 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=600, message=用户名或密码错误, success=false, data=null, time=2025-07-31T08:58:10.628229700Z)]
2025-07-31 16:58:10.649 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : Resolved [com.example.pure.exception.BusinessException: 用户名或密码错误]
2025-07-31 16:58:10.650 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 400 BAD_REQUEST
2025-07-31 16:58:10.650 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-31 16:58:17.594 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-07-31 16:58:17.594 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-31 16:58:17.594 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:17.595 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter : Set SecurityContextHolder to anonymous SecurityContext
2025-07-31 16:58:17.595 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-07-31 16:58:17.595 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-07-31 16:58:17.596 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-07-31 16:58:17.596 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:17.597 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=admin, password=a12345678, recaptchaToken=3, email=null, deviceId=null)]
2025-07-31 16:58:17.600 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.controller.auth.AuthController : 用户登录: admin
2025-07-31 16:58:17.600 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.auth.impl.AuthServiceImpl : 处理用户登录请求: admin
2025-07-31 16:58:17.603 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:17.628 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:17.628 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:17.628 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2871d463]
2025-07-31 16:58:17.628 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@792343231 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:17.628 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:17.629 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:17.632 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:17.632 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2871d463]
2025-07-31 16:58:17.632 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:17.632 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2871d463]
2025-07-31 16:58:17.632 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2871d463]
2025-07-31 16:58:17.632 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2871d463]
2025-07-31 16:58:17.701 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-07-31 16:58:17.904 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:accessToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addAccessTokenDevice(DeviceServiceImpl.java:99)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:143)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 130 common frames omitted
2025-07-31 16:58:17.916 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:admin, DeviceId - 5d501b47-aa51-4548-b4da-2595e6c8d41d
2025-07-31 16:58:17.920 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:refreshToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addRefreshTokenDevice(DeviceServiceImpl.java:104)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:150)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 130 common frames omitted
2025-07-31 16:58:17.924 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:admin, DeviceId - e79d6332-56de-4fae-9fc6-029540288c61
2025-07-31 16:58:17.939 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.user.impl.UserServiceImpl : 更新用户信息, userId: 1
2025-07-31 16:58:17.941 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:17.941 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cec135]
2025-07-31 16:58:17.958 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:17.958 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-31 16:58:17.959 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : ==> Parameters: admin(String), $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-31 16:58:17.9372086(Timestamp), $2a$10$Xw6RTK2X7w41QHJzhTzn1uIjXB0feBjlnmkajSZSsMtITHGSbvc/a(String), 2025-08-14 16:58:17.0(Timestamp), 1(Long)
2025-07-31 16:58:17.966 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-31 16:58:17.966 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cec135]
2025-07-31 16:58:17.967 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.user.impl.UserServiceImpl : 用户信息更新成功, userId: 1
2025-07-31 16:58:17.967 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cec135]
2025-07-31 16:58:17.967 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cec135]
2025-07-31 16:58:17.967 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2cec135]
2025-07-31 16:58:17.978 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.auth.impl.AuthServiceImpl : 用户登录成功: admin
2025-07-31 16:58:17.980 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.user.impl.UserServiceImpl : 根据用户名查找用户: admin
2025-07-31 16:58:17.982 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:17.982 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24993882]
2025-07-31 16:58:17.982 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1049819206 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:17.982 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.UserMapper.findByUsername : ==>  Preparing: SELECT id, username, password, created_time, last_login_time, updated_time FROM user WHERE username =?
2025-07-31 16:58:17.982 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.UserMapper.findByUsername : ==> Parameters: admin(String)
2025-07-31 16:58:17.984 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.UserMapper.findByUsername : <==      Total: 1
2025-07-31 16:58:17.984 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24993882]
2025-07-31 16:58:17.985 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24993882]
2025-07-31 16:58:17.985 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24993882]
2025-07-31 16:58:17.985 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24993882]
2025-07-31 16:58:17.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:17.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9]
2025-07-31 16:58:17.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1934358523 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:17.999 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-07-31 16:58:17.999 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 1(Long), LOGIN(String), 2025-07-31(String)
2025-07-31 16:58:18.002 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 0
2025-07-31 16:58:18.002 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9]
2025-07-31 16:58:18.004 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9] from current transaction
2025-07-31 16:58:18.004 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-07-31 16:58:18.004 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 1(Long), LOGIN(String), 2025-07-30(String)
2025-07-31 16:58:18.007 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 0
2025-07-31 16:58:18.008 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9]
2025-07-31 16:58:18.008 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9] from current transaction
2025-07-31 16:58:18.008 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.AccessLogMapper.insert : ==>  Preparing: INSERT INTO access_log ( user_id, access_type, access_count, access_date, created_time, updated_time, ip_address ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-31 16:58:18.009 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.AccessLogMapper.insert : ==> Parameters: 1(Long), LOGIN(String), 1(Integer), 2025-07-31(LocalDate), 2025-07-31T16:58:18.008050800(LocalDateTime), 2025-07-31T16:58:18.008050800(LocalDateTime), 127.0.0.1(String)
2025-07-31 16:58:18.013 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.AccessLogMapper.insert : <==    Updates: 1
2025-07-31 16:58:18.016 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9]
2025-07-31 16:58:18.016 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.a.impl.AccessLogServiceImpl : Created new access log for user: 1, type: LOGIN, count: 1
2025-07-31 16:58:18.016 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9]
2025-07-31 16:58:18.016 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9]
2025-07-31 16:58:18.016 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a5469a9]
2025-07-31 16:58:18.051 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:18.051 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7535ae4e]
2025-07-31 16:58:18.051 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@578383142 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:18.051 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-31 16:58:18.052 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 1(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户admin执行了登陆(String), 2025-07-31T16:58:18.047292100(LocalDateTime)
2025-07-31 16:58:18.054 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-31 16:58:18.054 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7535ae4e]
2025-07-31 16:58:18.054 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-4] c.e.p.s.a.i.OperatingLogServiceImpl : 记录用户操作日志: userId=1, summary=用户admin执行了登陆
2025-07-31 16:58:18.054 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7535ae4e]
2025-07-31 16:58:18.054 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7535ae4e]
2025-07-31 16:58:18.054 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7535ae4e]
2025-07-31 16:58:18.061 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 16:58:18.062 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=admin, accessToken=eyJhbGci (truncated)...]
2025-07-31 16:58:18.064 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-31 16:58:18.064 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-31 16:58:24.645 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-07-31 16:58:24.645 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-31 16:58:24.646 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:24.737 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:24.739 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:24.739 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:24.739 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@72f72bd4]
2025-07-31 16:58:24.739 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@441831844 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:24.739 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:24.739 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:24.741 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:24.741 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@72f72bd4]
2025-07-31 16:58:24.741 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:24.742 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@72f72bd4]
2025-07-31 16:58:24.742 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@72f72bd4]
2025-07-31 16:58:24.742 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@72f72bd4]
2025-07-31 16:58:24.744 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-07-31 16:58:24.744 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-07-31 16:58:24.745 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-07-31 16:58:24.745 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-07-31 16:58:24.745 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:24.746 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=admin, password=a12345678, recaptchaToken=3, email=null, deviceId=null)]
2025-07-31 16:58:24.749 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.controller.auth.AuthController : 用户登录: admin
2025-07-31 16:58:24.749 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.auth.impl.AuthServiceImpl : 处理用户登录请求: admin
2025-07-31 16:58:24.751 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:24.753 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:24.753 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:24.753 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1444067a]
2025-07-31 16:58:24.753 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1113654189 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:24.753 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:24.753 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:24.755 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:24.755 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1444067a]
2025-07-31 16:58:24.755 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:24.755 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1444067a]
2025-07-31 16:58:24.755 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1444067a]
2025-07-31 16:58:24.755 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1444067a]
2025-07-31 16:58:24.826 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-07-31 16:58:24.983 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:accessToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addAccessTokenDevice(DeviceServiceImpl.java:99)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:143)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:24.985 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:admin, DeviceId - 2fb7db14-2d6a-49fe-b2f2-c6086b2f606f
2025-07-31 16:58:24.990 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:refreshToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addRefreshTokenDevice(DeviceServiceImpl.java:104)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:150)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:24.992 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:admin, DeviceId - 322a985d-e186-48c8-8579-15ef61f496f2
2025-07-31 16:58:24.995 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.user.impl.UserServiceImpl : 更新用户信息, userId: 1
2025-07-31 16:58:24.996 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:24.996 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@402e4da2]
2025-07-31 16:58:24.996 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:24.996 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-31 16:58:24.996 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.primary.UserMapper.update : ==> Parameters: admin(String), $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-31 16:58:24.9942921(Timestamp), $2a$10$VnBEhoa6fOYM7qcfogibK.PLBSwlpvhOYWaCtxwqS//Sx/LHK5hsq(String), 2025-08-14 16:58:24.0(Timestamp), 1(Long)
2025-07-31 16:58:24.999 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-31 16:58:24.999 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@402e4da2]
2025-07-31 16:58:24.999 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.user.impl.UserServiceImpl : 用户信息更新成功, userId: 1
2025-07-31 16:58:25.000 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@402e4da2]
2025-07-31 16:58:25.000 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@402e4da2]
2025-07-31 16:58:25.000 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@402e4da2]
2025-07-31 16:58:25.017 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.auth.impl.AuthServiceImpl : 用户登录成功: admin
2025-07-31 16:58:25.019 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.user.impl.UserServiceImpl : 根据用户名查找用户: admin
2025-07-31 16:58:25.024 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:25.024 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3505d8c1]
2025-07-31 16:58:25.024 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@522022395 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:25.024 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==>  Preparing: SELECT * FROM access_log WHERE user_id = ? AND access_type = ? AND DATE(access_date) = ?
2025-07-31 16:58:25.024 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : ==> Parameters: 1(Long), LOGIN(String), 2025-07-31(String)
2025-07-31 16:58:25.027 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.A.findByUserIdAndTypeAndDate : <==      Total: 1
2025-07-31 16:58:25.027 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3505d8c1]
2025-07-31 16:58:25.028 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3505d8c1]
2025-07-31 16:58:25.028 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3505d8c1]
2025-07-31 16:58:25.028 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3505d8c1]
2025-07-31 16:58:25.035 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:25.035 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ebda3e3]
2025-07-31 16:58:25.035 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1678942089 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:25.035 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-31 16:58:25.035 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 1(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户admin执行了登陆(String), 2025-07-31T16:58:25.034199600(LocalDateTime)
2025-07-31 16:58:25.037 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-31 16:58:25.037 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ebda3e3]
2025-07-31 16:58:25.037 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-5] c.e.p.s.a.i.OperatingLogServiceImpl : 记录用户操作日志: userId=1, summary=用户admin执行了登陆
2025-07-31 16:58:25.037 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ebda3e3]
2025-07-31 16:58:25.037 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ebda3e3]
2025-07-31 16:58:25.037 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ebda3e3]
2025-07-31 16:58:25.043 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 16:58:25.044 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=admin, accessToken=eyJhbGci (truncated)...]
2025-07-31 16:58:25.045 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-31 16:58:25.045 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-31 16:58:31.898 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-07-31 16:58:31.898 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-31 16:58:31.898 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:31.981 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:31.983 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:31.983 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:31.983 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f7ceee]
2025-07-31 16:58:31.983 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1507423151 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:31.984 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:31.984 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:31.985 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:31.985 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f7ceee]
2025-07-31 16:58:31.985 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:31.985 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f7ceee]
2025-07-31 16:58:31.986 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f7ceee]
2025-07-31 16:58:31.986 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59f7ceee]
2025-07-31 16:58:31.988 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-07-31 16:58:31.988 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-07-31 16:58:31.988 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-07-31 16:58:31.989 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-07-31 16:58:31.989 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:31.990 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=admin, password=a12345678, recaptchaToken=3, email=null, deviceId=null)]
2025-07-31 16:58:31.992 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.controller.auth.AuthController : 用户登录: admin
2025-07-31 16:58:31.992 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.auth.impl.AuthServiceImpl : 处理用户登录请求: admin
2025-07-31 16:58:31.994 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:31.995 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:31.995 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:31.995 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2962e6ad]
2025-07-31 16:58:31.995 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1762057267 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:31.995 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:31.996 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:31.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:31.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2962e6ad]
2025-07-31 16:58:31.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:31.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2962e6ad]
2025-07-31 16:58:31.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2962e6ad]
2025-07-31 16:58:31.998 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2962e6ad]
2025-07-31 16:58:32.068 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-07-31 16:58:32.228 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:accessToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addAccessTokenDevice(DeviceServiceImpl.java:99)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:143)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:32.231 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:admin, DeviceId - 7bec5f39-d011-4165-9ef6-02985e51594d
2025-07-31 16:58:32.234 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:refreshToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addRefreshTokenDevice(DeviceServiceImpl.java:104)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:150)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:32.236 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:admin, DeviceId - 4eed3583-2189-4767-b3bc-a41faed61528
2025-07-31 16:58:32.239 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.user.impl.UserServiceImpl : 更新用户信息, userId: 1
2025-07-31 16:58:32.239 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:32.239 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37eb91b3]
2025-07-31 16:58:32.240 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:32.240 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-31 16:58:32.240 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.primary.UserMapper.update : ==> Parameters: admin(String), $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-31 16:58:32.238155(Timestamp), $2a$10$MEy/YzT9qWHdb903Toypi.d8lKhXb0kqNPqmDhS9WruoSZHgAfPcK(String), 2025-08-14 16:58:32.0(Timestamp), 1(Long)
2025-07-31 16:58:32.243 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-31 16:58:32.243 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37eb91b3]
2025-07-31 16:58:32.244 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.user.impl.UserServiceImpl : 用户信息更新成功, userId: 1
2025-07-31 16:58:32.244 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37eb91b3]
2025-07-31 16:58:32.244 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37eb91b3]
2025-07-31 16:58:32.244 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37eb91b3]
2025-07-31 16:58:32.250 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.auth.impl.AuthServiceImpl : 用户登录成功: admin
2025-07-31 16:58:32.253 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.user.impl.UserServiceImpl : 根据用户名查找用户: admin
2025-07-31 16:58:32.265 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:32.265 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29cd8e69]
2025-07-31 16:58:32.265 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@894326792 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:32.265 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-31 16:58:32.266 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 1(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户admin执行了登陆(String), 2025-07-31T16:58:32.264149700(LocalDateTime)
2025-07-31 16:58:32.268 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-31 16:58:32.268 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29cd8e69]
2025-07-31 16:58:32.268 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-10] c.e.p.s.a.i.OperatingLogServiceImpl : 记录用户操作日志: userId=1, summary=用户admin执行了登陆
2025-07-31 16:58:32.268 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29cd8e69]
2025-07-31 16:58:32.268 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29cd8e69]
2025-07-31 16:58:32.268 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29cd8e69]
2025-07-31 16:58:32.275 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 16:58:32.275 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=admin, accessToken=eyJhbGci (truncated)...]
2025-07-31 16:58:32.276 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-31 16:58:32.277 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-10] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-31 16:58:35.349 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-07-31 16:58:35.349 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-31 16:58:35.349 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:35.424 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:35.425 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:35.425 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:35.425 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e5bbbaa]
2025-07-31 16:58:35.426 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@554839347 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:35.426 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:35.426 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:35.428 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:35.428 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e5bbbaa]
2025-07-31 16:58:35.428 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:35.428 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e5bbbaa]
2025-07-31 16:58:35.428 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e5bbbaa]
2025-07-31 16:58:35.428 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e5bbbaa]
2025-07-31 16:58:35.431 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-07-31 16:58:35.431 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-07-31 16:58:35.431 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-07-31 16:58:35.431 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-07-31 16:58:35.432 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:35.433 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=admin, password=a12345678, recaptchaToken=3, email=null, deviceId=null)]
2025-07-31 16:58:35.436 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.controller.auth.AuthController : 用户登录: admin
2025-07-31 16:58:35.436 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.auth.impl.AuthServiceImpl : 处理用户登录请求: admin
2025-07-31 16:58:35.438 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:35.440 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:35.441 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:35.441 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7563ff56]
2025-07-31 16:58:35.441 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1909377288 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:35.441 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:35.441 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:35.443 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:35.443 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7563ff56]
2025-07-31 16:58:35.443 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:35.444 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7563ff56]
2025-07-31 16:58:35.444 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7563ff56]
2025-07-31 16:58:35.444 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7563ff56]
2025-07-31 16:58:35.513 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-07-31 16:58:35.670 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:accessToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addAccessTokenDevice(DeviceServiceImpl.java:99)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:143)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:35.672 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:admin, DeviceId - 91706d63-f033-4dcb-babb-22460cad8c64
2025-07-31 16:58:35.676 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:refreshToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addRefreshTokenDevice(DeviceServiceImpl.java:104)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:150)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:35.678 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:admin, DeviceId - 7df1043c-bd5b-4edb-8def-079d41b074a7
2025-07-31 16:58:35.682 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.user.impl.UserServiceImpl : 更新用户信息, userId: 1
2025-07-31 16:58:35.682 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:35.682 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fc519b4]
2025-07-31 16:58:35.682 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@******** wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:35.682 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-31 16:58:35.683 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.primary.UserMapper.update : ==> Parameters: admin(String), $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-31 16:58:35.6804374(Timestamp), $2a$10$O71IyO/Q1fnrJneiazqeEu3BWj/KCtKOG2t93DaelGlgMoTQUNomG(String), 2025-08-14 16:58:35.0(Timestamp), 1(Long)
2025-07-31 16:58:35.686 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-31 16:58:35.686 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fc519b4]
2025-07-31 16:58:35.686 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.user.impl.UserServiceImpl : 用户信息更新成功, userId: 1
2025-07-31 16:58:35.686 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fc519b4]
2025-07-31 16:58:35.686 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fc519b4]
2025-07-31 16:58:35.686 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fc519b4]
2025-07-31 16:58:35.704 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.auth.impl.AuthServiceImpl : 用户登录成功: admin
2025-07-31 16:58:35.706 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.user.impl.UserServiceImpl : 根据用户名查找用户: admin
2025-07-31 16:58:35.717 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:35.717 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b95b37]
2025-07-31 16:58:35.717 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1040335878 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:35.717 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-31 16:58:35.717 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 1(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户admin执行了登陆(String), 2025-07-31T16:58:35.715986200(LocalDateTime)
2025-07-31 16:58:35.719 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-31 16:58:35.719 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b95b37]
2025-07-31 16:58:35.720 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-7] c.e.p.s.a.i.OperatingLogServiceImpl : 记录用户操作日志: userId=1, summary=用户admin执行了登陆
2025-07-31 16:58:35.720 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b95b37]
2025-07-31 16:58:35.720 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b95b37]
2025-07-31 16:58:35.720 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b95b37]
2025-07-31 16:58:35.726 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 16:58:35.727 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=admin, accessToken=eyJhbGci (truncated)...]
2025-07-31 16:58:35.728 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-31 16:58:35.728 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-31 16:58:36.796 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-07-31 16:58:36.796 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-31 16:58:36.796 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:36.875 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:36.876 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:36.876 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:36.876 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1314b5e4]
2025-07-31 16:58:36.876 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@69265787 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:36.876 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:36.877 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:36.878 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:36.879 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1314b5e4]
2025-07-31 16:58:36.879 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:36.879 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1314b5e4]
2025-07-31 16:58:36.879 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1314b5e4]
2025-07-31 16:58:36.879 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1314b5e4]
2025-07-31 16:58:36.882 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-07-31 16:58:36.882 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-07-31 16:58:36.882 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-07-31 16:58:36.882 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-07-31 16:58:36.883 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:36.883 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=admin, password=a12345678, recaptchaToken=3, email=null, deviceId=null)]
2025-07-31 16:58:36.886 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.controller.auth.AuthController : 用户登录: admin
2025-07-31 16:58:36.886 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.auth.impl.AuthServiceImpl : 处理用户登录请求: admin
2025-07-31 16:58:36.888 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:36.890 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:36.890 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:36.890 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@103f033a]
2025-07-31 16:58:36.890 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1352976946 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:36.890 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:36.890 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:36.892 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:36.892 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@103f033a]
2025-07-31 16:58:36.892 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:36.892 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@103f033a]
2025-07-31 16:58:36.892 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@103f033a]
2025-07-31 16:58:36.892 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@103f033a]
2025-07-31 16:58:36.964 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-07-31 16:58:37.124 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:accessToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addAccessTokenDevice(DeviceServiceImpl.java:99)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:143)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:37.126 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:admin, DeviceId - 1f7c2fff-28d4-472d-a66f-a961b7953ebe
2025-07-31 16:58:37.130 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:refreshToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addRefreshTokenDevice(DeviceServiceImpl.java:104)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:150)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:37.131 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:admin, DeviceId - 7ddc5ba7-1412-41cd-aba5-dd150c3654e3
2025-07-31 16:58:37.134 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.user.impl.UserServiceImpl : 更新用户信息, userId: 1
2025-07-31 16:58:37.134 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:37.134 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efa1396]
2025-07-31 16:58:37.135 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:37.135 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-31 16:58:37.135 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.primary.UserMapper.update : ==> Parameters: admin(String), $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-31 16:58:37.1335805(Timestamp), $2a$10$miDHf1zKb3sXhRdsI5XW0.Yz95ybfyMqLLnK22luMMqqLmyjYdabu(String), 2025-08-14 16:58:36.0(Timestamp), 1(Long)
2025-07-31 16:58:37.138 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-31 16:58:37.138 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efa1396]
2025-07-31 16:58:37.138 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.user.impl.UserServiceImpl : 用户信息更新成功, userId: 1
2025-07-31 16:58:37.138 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efa1396]
2025-07-31 16:58:37.138 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efa1396]
2025-07-31 16:58:37.138 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efa1396]
2025-07-31 16:58:37.147 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.auth.impl.AuthServiceImpl : 用户登录成功: admin
2025-07-31 16:58:37.149 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.user.impl.UserServiceImpl : 根据用户名查找用户: admin
2025-07-31 16:58:37.159 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:37.159 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55b3e495]
2025-07-31 16:58:37.159 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1934435947 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:37.159 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-31 16:58:37.160 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 1(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户admin执行了登陆(String), 2025-07-31T16:58:37.158581900(LocalDateTime)
2025-07-31 16:58:37.162 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-31 16:58:37.162 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55b3e495]
2025-07-31 16:58:37.162 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-8] c.e.p.s.a.i.OperatingLogServiceImpl : 记录用户操作日志: userId=1, summary=用户admin执行了登陆
2025-07-31 16:58:37.162 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55b3e495]
2025-07-31 16:58:37.162 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55b3e495]
2025-07-31 16:58:37.162 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55b3e495]
2025-07-31 16:58:37.169 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 16:58:37.169 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=admin, accessToken=eyJhbGci (truncated)...]
2025-07-31 16:58:37.170 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-31 16:58:37.170 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-8] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-31 16:58:43.477 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-07-31 16:58:43.477 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-31 16:58:43.477 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:43.569 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:43.573 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:43.573 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:43.573 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cc22cdc]
2025-07-31 16:58:43.573 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@699023767 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:43.573 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:43.574 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:43.577 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:43.577 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cc22cdc]
2025-07-31 16:58:43.578 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:43.578 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cc22cdc]
2025-07-31 16:58:43.578 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cc22cdc]
2025-07-31 16:58:43.578 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cc22cdc]
2025-07-31 16:58:43.583 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-07-31 16:58:43.584 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-07-31 16:58:43.584 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-07-31 16:58:43.584 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-07-31 16:58:43.584 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:43.585 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=admin, password=a12345678, recaptchaToken=3, email=null, deviceId=null)]
2025-07-31 16:58:43.589 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.controller.auth.AuthController : 用户登录: admin
2025-07-31 16:58:43.590 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.auth.impl.AuthServiceImpl : 处理用户登录请求: admin
2025-07-31 16:58:43.591 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:43.594 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:43.594 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:43.594 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6542ce05]
2025-07-31 16:58:43.594 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@27766838 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:43.595 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:43.595 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:43.597 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:43.598 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6542ce05]
2025-07-31 16:58:43.598 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:43.598 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6542ce05]
2025-07-31 16:58:43.598 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6542ce05]
2025-07-31 16:58:43.598 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6542ce05]
2025-07-31 16:58:43.678 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-07-31 16:58:43.853 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:accessToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addAccessTokenDevice(DeviceServiceImpl.java:99)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:143)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:43.855 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:admin, DeviceId - b280b3ac-2a88-4252-be7c-acc3f2a2ce3e
2025-07-31 16:58:43.861 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:refreshToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addRefreshTokenDevice(DeviceServiceImpl.java:104)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:150)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:43.863 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:admin, DeviceId - ef678215-5eb0-4639-98f7-778cbeaccc67
2025-07-31 16:58:43.868 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.user.impl.UserServiceImpl : 更新用户信息, userId: 1
2025-07-31 16:58:43.868 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:43.868 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b4a0db8]
2025-07-31 16:58:43.869 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********* wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:43.869 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-31 16:58:43.869 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.primary.UserMapper.update : ==> Parameters: admin(String), $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-31 16:58:43.8664799(Timestamp), $2a$10$jwvtHK4L1BORmQ.kM0GzGuVwU/rOuSOF11kbCGbPxFYr/tAVIFKS.(String), 2025-08-14 16:58:43.0(Timestamp), 1(Long)
2025-07-31 16:58:43.872 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-31 16:58:43.872 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b4a0db8]
2025-07-31 16:58:43.873 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.user.impl.UserServiceImpl : 用户信息更新成功, userId: 1
2025-07-31 16:58:43.873 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b4a0db8]
2025-07-31 16:58:43.873 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b4a0db8]
2025-07-31 16:58:43.873 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b4a0db8]
2025-07-31 16:58:43.881 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.auth.impl.AuthServiceImpl : 用户登录成功: admin
2025-07-31 16:58:43.883 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.user.impl.UserServiceImpl : 根据用户名查找用户: admin
2025-07-31 16:58:43.898 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:43.898 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67f92b81]
2025-07-31 16:58:43.898 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@11266252 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:43.898 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-31 16:58:43.899 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 1(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户admin执行了登陆(String), 2025-07-31T16:58:43.897261900(LocalDateTime)
2025-07-31 16:58:43.901 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-31 16:58:43.902 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67f92b81]
2025-07-31 16:58:43.902 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-9] c.e.p.s.a.i.OperatingLogServiceImpl : 记录用户操作日志: userId=1, summary=用户admin执行了登陆
2025-07-31 16:58:43.902 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67f92b81]
2025-07-31 16:58:43.902 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67f92b81]
2025-07-31 16:58:43.902 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67f92b81]
2025-07-31 16:58:43.910 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 16:58:43.910 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=admin, accessToken=eyJhbGci (truncated)...]
2025-07-31 16:58:43.912 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-31 16:58:43.912 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-9] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-07-31 16:58:45.125 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Securing POST /api/auth/login
2025-07-31 16:58:45.125 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-07-31 16:58:45.126 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:45.202 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:45.203 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:45.203 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:45.203 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@231dd8f]
2025-07-31 16:58:45.204 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1742583222 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:45.204 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:45.204 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:45.206 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:45.206 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@231dd8f]
2025-07-31 16:58:45.206 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:45.206 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@231dd8f]
2025-07-31 16:58:45.206 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@231dd8f]
2025-07-31 16:58:45.206 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@231dd8f]
2025-07-31 16:58:45.208 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-07-31 16:58:45.208 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/auth/login] with attributes [permitAll]
2025-07-31 16:58:45.208 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.security.web.FilterChainProxy : Secured POST /api/auth/login
2025-07-31 16:58:45.209 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : POST "/api/auth/login", parameters={}
2025-07-31 16:58:45.209 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.auth.AuthController#login(LoginRequest, HttpServletRequest, HttpServletResponse)
2025-07-31 16:58:45.209 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [LoginRequest(username=admin, password=a12345678, recaptchaToken=3, email=null, deviceId=null)]
2025-07-31 16:58:45.212 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.controller.auth.AuthController : 用户登录: admin
2025-07-31 16:58:45.212 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.auth.impl.AuthServiceImpl : 处理用户登录请求: admin
2025-07-31 16:58:45.214 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-07-31 16:58:45.215 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-07-31 16:58:45.215 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:45.215 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@688581ae]
2025-07-31 16:58:45.215 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@229942974 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:45.216 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-07-31 16:58:45.216 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-07-31 16:58:45.217 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-07-31 16:58:45.218 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@688581ae]
2025-07-31 16:58:45.218 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-07-31 16:58:45.218 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@688581ae]
2025-07-31 16:58:45.218 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@688581ae]
2025-07-31 16:58:45.218 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@688581ae]
2025-07-31 16:58:45.288 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.s.a.d.DaoAuthenticationProvider : Authenticated user
2025-07-31 16:58:45.441 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:accessToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addAccessTokenDevice(DeviceServiceImpl.java:99)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:143)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"9cc064f5-c33a-42b1-9000-c50b1591ec58","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.350Z","token":"$2a$10$NXRadKq4b3cP4iJqUH1T2uXoJ/I1trUZEGSmzusk.GFO5RRd3TKry","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:45.443 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:accessToken:admin, DeviceId - b5b2b2e1-7b10-4ba8-8b04-a682680fb8b0
2025-07-31 16:58:45.446 [1;31mERROR[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.auth.impl.DeviceServiceImpl : 获取用户设备列表失败: Key - user:devices:refreshToken:admin
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]; nested exception is com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:165)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:143)
	at org.springframework.data.redis.serializer.SerializationUtils.deserializeValues(SerializationUtils.java:54)
	at org.springframework.data.redis.serializer.SerializationUtils.deserialize(SerializationUtils.java:68)
	at org.springframework.data.redis.core.AbstractOperations.deserializeHashValues(AbstractOperations.java:310)
	at org.springframework.data.redis.core.DefaultHashOperations.values(DefaultHashOperations.java:283)
	at com.example.pure.util.RedisUtil.getHashValues(RedisUtil.java:250)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.getDevices(DeviceServiceImpl.java:174)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addDevice(DeviceServiceImpl.java:126)
	at com.example.pure.service.auth.impl.DeviceServiceImpl.addRefreshTokenDevice(DeviceServiceImpl.java:104)
	at com.example.pure.service.auth.impl.AuthServiceImpl.login(AuthServiceImpl.java:150)
	at com.example.pure.controller.auth.AuthController.login(AuthController.java:132)
	at com.example.pure.controller.auth.AuthController$$FastClassBySpringCGLIB$$d358d57d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.pure.controller.auth.AuthController$$EnhancerBySpringCGLIB$$6f4ae4b4.login(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.pure.filter.JwtFilter.doFilterInternal(JwtFilter.java:170)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidTypeIdException: Could not resolve type id 'com.example.pure.model.dto.DeviceInfo' as a subtype of `java.lang.Object`: no such class found
 at [Source: (byte[])"{"@class":"com.example.pure.model.dto.DeviceInfo","deviceId":"8e2f0565-f14d-453e-bac0-9eb70c72b41a","deviceType":"UNKNOWN","loginTime":"2025-07-19T22:37:57Z","lastActiveTime":"2025-07-19T22:37:57.364Z","token":"$2a$10$gnaYEsEvZK2OGY4rvesTmOeYBTEUIx9biekCK2ynhdavf27G5gRlO","ipAddress":"127.0.0.1","userAgent":"PostmanRuntime-ApipostRuntime/1.1.0"}"; line: 1, column: 11]
	at com.fasterxml.jackson.databind.exc.InvalidTypeIdException.from(InvalidTypeIdException.java:43)
	at com.fasterxml.jackson.databind.DeserializationContext.invalidTypeIdException(DeserializationContext.java:2073)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownTypeId(DeserializationContext.java:1564)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver._typeFromId(ClassNameIdResolver.java:76)
	at com.fasterxml.jackson.databind.jsontype.impl.ClassNameIdResolver.typeFromId(ClassNameIdResolver.java:66)
	at com.fasterxml.jackson.databind.jsontype.impl.TypeDeserializerBase._findDeserializer(TypeDeserializerBase.java:159)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:125)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:110)
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromAny(AsPropertyTypeDeserializer.java:213)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3690)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:163)
	... 131 common frames omitted
2025-07-31 16:58:45.448 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.auth.impl.DeviceServiceImpl : 添加设备成功: Key - user:devices:refreshToken:admin, DeviceId - 52ed466a-3fe5-4ccf-bfae-222766377b5f
2025-07-31 16:58:45.450 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.user.impl.UserServiceImpl : 更新用户信息, userId: 1
2025-07-31 16:58:45.450 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:45.450 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14ab4291]
2025-07-31 16:58:45.451 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:45.451 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : ==>  Preparing: UPDATE user SET username = ?, password = ?, enabled = ?, account_non_expired = ?, account_non_locked = ?, credentials_non_expired = ?, last_login_time = ?, refresh_token = ?, refresh_token_expires = ?, updated_time = NOW() WHERE id = ?
2025-07-31 16:58:45.451 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : ==> Parameters: admin(String), $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW(String), true(Boolean), true(Boolean), true(Boolean), true(Boolean), 2025-07-31 16:58:45.4494321(Timestamp), $2a$10$lRrhrvzYgJihdL00f31th.1q6WLcduBF4BGww2sBBbk.1p2UsnniO(String), 2025-08-14 16:58:45.0(Timestamp), 1(Long)
2025-07-31 16:58:45.453 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.primary.UserMapper.update : <==    Updates: 1
2025-07-31 16:58:45.453 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14ab4291]
2025-07-31 16:58:45.454 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.user.impl.UserServiceImpl : 用户信息更新成功, userId: 1
2025-07-31 16:58:45.454 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14ab4291]
2025-07-31 16:58:45.454 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14ab4291]
2025-07-31 16:58:45.454 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14ab4291]
2025-07-31 16:58:45.471 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.auth.impl.AuthServiceImpl : 用户登录成功: admin
2025-07-31 16:58:45.472 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.user.impl.UserServiceImpl : 根据用户名查找用户: admin
2025-07-31 16:58:45.480 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-07-31 16:58:45.480 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6d6a0ae7]
2025-07-31 16:58:45.480 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@864875999 wrapping com.mysql.cj.jdbc.ConnectionImpl@57036c2f] will be managed by Spring
2025-07-31 16:58:45.480 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : ==>  Preparing: INSERT INTO operating_log ( user_id, ip, address, system_info, browser, summary, operating_time ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
2025-07-31 16:58:45.480 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : ==> Parameters: 1(Long), 0:0:0:0:0:0:0:1(String), 未知(String), 其他(String), 其他(String), 用户admin执行了登陆(String), 2025-07-31T16:58:45.479999600(LocalDateTime)
2025-07-31 16:58:45.483 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.m.p.O.insertOperatingLog : <==    Updates: 1
2025-07-31 16:58:45.483 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6d6a0ae7]
2025-07-31 16:58:45.483 [34mINFO [0;39m 8128 --- [http-nio-8080-exec-6] c.e.p.s.a.i.OperatingLogServiceImpl : 记录用户操作日志: userId=1, summary=用户admin执行了登陆
2025-07-31 16:58:45.483 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6d6a0ae7]
2025-07-31 16:58:45.483 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6d6a0ae7]
2025-07-31 16:58:45.483 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6d6a0ae7]
2025-07-31 16:58:45.490 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 16:58:45.490 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=登录成功, success=true, data=TokenResponse(username=admin, accessToken=eyJhbGci (truncated)...]
2025-07-31 16:58:45.490 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-07-31 16:58:45.490 [39mDEBUG[0;39m 8128 --- [http-nio-8080-exec-6] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
