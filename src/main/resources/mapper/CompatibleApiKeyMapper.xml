<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 兼容API密钥Mapper配置 -->
<mapper namespace="com.example.pure.mapper.primary.CompatibleApiKeyMapper">

    <!-- 兼容API密钥结果映射 -->
    <resultMap id="CompatibleApiKeyResultMap" type="com.example.pure.model.entity.CompatibleApiKey">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="keyName" column="key_name"/>
        <result property="keyHash" column="key_hash"/>
        <result property="salt" column="salt"/>
        <result property="usageCount" column="usage_count"/>
        <result property="lastUsedAt" column="last_used_at"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, user_id, key_name, key_hash, salt,
        usage_count, last_used_at, created_at, updated_at
    </sql>

    <!-- 根据ID查询兼容API密钥 -->
    <select id="selectById" resultMap="CompatibleApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_compatible_api_keys
        WHERE id = #{id}
    </select>

    <!-- 根据密钥哈希查询兼容API密钥 -->
    <select id="selectByKeyHash" resultMap="CompatibleApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_compatible_api_keys
        WHERE key_hash = #{keyHash}
    </select>

    <!-- 根据用户ID查询所有兼容API密钥 -->
    <select id="selectByUserId" resultMap="CompatibleApiKeyResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_compatible_api_keys
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
    </select>

    <!-- 插入兼容API密钥 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.CompatibleApiKey"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO ai_compatible_api_keys (
            user_id, key_name, key_hash, salt, usage_count
        ) VALUES (
            #{userId}, #{keyName}, #{keyHash}, #{salt}, #{usageCount}
        )
    </insert>

    <!-- 更新兼容API密钥 -->
    <update id="updateById" parameterType="com.example.pure.model.entity.CompatibleApiKey">
        UPDATE ai_compatible_api_keys
        SET key_name = #{keyName},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新使用统计 -->
    <update id="updateUsageStats">
        UPDATE ai_compatible_api_keys
        SET usage_count = usage_count + 1,
            last_used_at = NOW(),
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据密钥哈希更新使用统计 -->
    <update id="updateUsageStatsByKeyHash">
        UPDATE ai_compatible_api_keys
        SET usage_count = usage_count + 1,
            last_used_at = NOW(),
            updated_at = NOW()
        WHERE key_hash = #{keyHash}
    </update>

    <!-- 删除兼容API密钥 -->
    <delete id="deleteById">
        DELETE FROM ai_compatible_api_keys
        WHERE id = #{id}
    </delete>

    <!-- 根据用户ID删除所有兼容API密钥 -->
    <delete id="deleteByUserId">
        DELETE FROM ai_compatible_api_keys
        WHERE user_id = #{userId}
    </delete>

    <!-- 检查密钥名称是否已存在 -->
    <select id="countByUserIdAndKeyName" resultType="int">
        SELECT COUNT(*)
        FROM ai_compatible_api_keys
        WHERE user_id = #{userId} AND key_name = #{keyName}
    </select>

</mapper>
