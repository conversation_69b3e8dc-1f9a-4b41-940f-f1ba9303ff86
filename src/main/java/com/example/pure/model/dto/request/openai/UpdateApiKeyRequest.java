package com.example.pure.model.dto.request.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 更新API密钥请求DTO
 * <p>
 * 用于更新现有的API密钥
 * </p>
 */
@Data
@Schema(description = "更新API密钥请求")
public class UpdateApiKeyRequest {

    /**
     * API密钥（可选，为空则不更新）
     */
    @Schema(description = "API密钥（可选，为空则不更新）", example = "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
    private String apiKey;

    /**
     * 优先级
     */
    @Min(value = 1, message = "优先级不能小于1")
    @Max(value = 100, message = "优先级不能大于100")
    @Schema(description = "优先级（数字越小优先级越高）", example = "1")
    private Integer priority;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;
}
