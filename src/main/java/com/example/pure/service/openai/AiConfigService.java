package com.example.pure.service.openai;

import com.example.pure.model.dto.response.openai.ApiKeyParseResult;
import com.example.pure.model.dto.response.openai.ApiKeyTestResult;
import com.example.pure.model.entity.UserAiConfig;
import com.example.pure.model.entity.UserApiKey;

import java.util.List;

/**
 * AI配置管理服务接口
 * <p>
 * 提供用户AI配置和API密钥的管理功能
 * </p>
 */
public interface AiConfigService {

    // ========================
    // 用户AI配置管理
    // ========================

    /**
     * 获取用户AI配置
     *
     * @param userId 用户ID
     * @return 用户AI配置，如果不存在则返回默认配置
     */
    UserAiConfig getUserConfig(Long userId);

    /**
     * 更新用户AI配置
     *
     * @param userId 用户ID
     * @param config 配置信息
     * @return 更新后的配置
     */
    UserAiConfig updateUserConfig(Long userId, UserAiConfig config);

    /**
     * 重置用户AI配置为默认值
     *
     * @param userId 用户ID
     * @return 重置后的配置
     */
    UserAiConfig resetUserConfig(Long userId);

    // ========================
    // API密钥管理
    // ========================

    /**
     * 获取用户的所有API密钥
     *
     * @param userId 用户ID
     * @return API密钥列表（已脱敏）
     */
    List<UserApiKey> getUserApiKeys(Long userId);

    /**
     * 根据提供商获取用户的API密钥
     *
     * @param userId   用户ID
     * @param provider 提供商类型
     * @return API密钥列表
     */
    List<UserApiKey> getUserApiKeysByProvider(Long userId, UserApiKey.ProviderType provider);

    /**
     * 添加API密钥
     *
     * @param userId    用户ID
     * @param provider  提供商类型
     * @param keyName   密钥名称
     * @param apiKey    原始API密钥
     * @param priority  优先级
     * @return 添加的API密钥信息（已脱敏）
     */
    UserApiKey addApiKey(Long userId, UserApiKey.ProviderType provider,  String apiKey, Integer priority);

    /**
     * 更新API密钥
     *
     * @param userId  用户ID
     * @param keyId   密钥ID
     * @param keyName 密钥名称
     * @param apiKey  原始API密钥（可选，为空则不更新）
     * @param priority 优先级
     * @param isActive 是否激活
     * @return 更新后的API密钥信息（已脱敏）
     */
    UserApiKey updateApiKey(Long userId, Long keyId,  String apiKey, Integer priority, Boolean isActive);

    /**
     * 删除API密钥
     *
     * @param userId 用户ID
     * @param keyId  密钥ID
     * @return 是否删除成功
     */
    boolean deleteApiKey(Long userId, Long keyId);

    /**
     * 测试API密钥有效性
     *
     * @param userId 用户ID
     * @param keyId  密钥ID
     * @return 测试结果
     */
    ApiKeyTestResult testApiKey(Long userId, Long keyId);

    // ========================
    // 兼容密钥功能已迁移到 CompatibleKeyService
    // ========================

    // ========================
    // 注意：相关的数据传输对象已移动到独立的包中
    // 请参考：com.example.pure.model.config 包
    // ========================
}
