package com.example.pure.service.openai.impl;

import com.example.pure.config.AiConfig;
import com.example.pure.model.adapter.*;
import com.example.pure.model.dto.request.openai.OpenAiImageRequest;
import com.example.pure.model.dto.response.openai.OpenAiImageResponse;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.ModelAdapterService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 模型适配服务实现类
 * <p>
 * 实现不同AI提供商的API格式适配和统一调用接口
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelAdapterServiceImpl implements ModelAdapterService {

    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private final AiConfig aiConfig;

    // 超时配置 - 增加超时时间以支持更长的流式响应
    private static final Duration REQUEST_TIMEOUT = Duration.ofSeconds(120);
    private static final Duration STREAM_TIMEOUT = Duration.ofMinutes(10);

    @Override
    public Flux<ChatCompletionChunk> streamChatCompletion(UserApiKey.ProviderType provider, String apiKey, ChatCompletionRequest request) {
        log.debug("发起流式聊天请求 - 提供商: {}, 模型: {}", provider, request.getModel());
        return streamOpenAiCompatibleChatCompletion(provider, apiKey, request);
    }

    @Override
    public Mono<ChatCompletionResponse> chatCompletion(UserApiKey.ProviderType provider, String apiKey, ChatCompletionRequest request) {
        log.debug("发起非流式聊天请求 - 提供商: {}, 模型: {}", provider, request.getModel());

        // 设置为非流式
        request.setStream(false);

        return openAiCompatibleChatCompletion(provider, apiKey, request);
    }

    @Override
    public Mono<List<ModelInfo>> getAvailableModels(UserApiKey.ProviderType provider, String apiKey) {
        log.debug("获取可用模型列表 - 提供商: {}", provider);
        return getOpenAiCompatibleModels(provider, apiKey);
    }

    @Override
    public Mono<OpenAiImageResponse> generateImage(UserApiKey.ProviderType provider, String apiKey, OpenAiImageRequest request) {
        log.debug("发起图片生成请求 - 提供商: {}, 模型: {}", provider, request.getModel());

        switch (provider) {
            case OPENAI:
                return generateOpenAiImage(apiKey, request);
            case GOOGLE:
                return generateGeminiImage(apiKey, request);
            case ANTHROPIC:
                return Mono.error(new UnsupportedOperationException("Claude不支持图片生成"));
            default:
                return Mono.error(new UnsupportedOperationException("不支持的提供商: " + provider));
        }
    }

    @Override
    public Mono<ApiKeyValidationResult> validateApiKey(UserApiKey.ProviderType provider, String apiKey) {
        log.debug("验证API密钥 - 提供商: {}", provider);
        return validateOpenAiCompatibleApiKey(provider, apiKey);
    }

    // ========================
    // 统一流式聊天完成实现
    // ========================

    /**
     * OpenAI兼容格式的流式聊天完成方法
     * 根据不同的提供商配置相应的请求参数和处理逻辑，统一使用OpenAI格式
     */
    private Flux<ChatCompletionChunk> streamOpenAiCompatibleChatCompletion(UserApiKey.ProviderType provider, String apiKey, ChatCompletionRequest request) {
        // 根据提供商获取配置信息
        ProviderConfig config = getProviderConfig(provider);

        // 构建请求体
        Map<String, Object> requestBody = buildOpenAiRequestBody(request, true, provider);

        // 决策理由：添加详细的请求日志来诊断不同提供商的问题
        log.info("=== {} 流式请求详情 ===", config.name);
        log.info("请求URL: {}", config.url);
        log.info("请求头: Authorization=Bearer {}...", apiKey.substring(0, Math.min(10, apiKey.length())));
        if (config.additionalHeaders != null) {
            config.additionalHeaders.forEach((key, value) ->
                log.info("请求头: {}={}", key, value));
        }
        log.info("请求体: {}", requestBody);

        /* 从webClient.post()到.doOnNext之前定义流程(相当于定义方法还没运行)使用.subscribe()运行然后doOnSubscribe方法执行，
        然后这里定义的流程开始启动，发送post请求，之后每次接收到数据的时候.doOnNext执行之后的方法直到返回chunk对象给外面
        的doOnNext方法接收，
        * */
        // 构建WebClient请求
        WebClient.RequestBodySpec requestSpec = webClient.post()
                .uri(config.url)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        // 添加提供商特定的请求头
        if (config.additionalHeaders != null) {
            config.additionalHeaders.forEach((key, value) -> requestSpec.header(key, value));
        }

        return requestSpec
                .bodyValue(requestBody)
                .retrieve()
                //返回单元素(单对象)用Mono,多个元素(集合或对象列表)用Flux
                .bodyToFlux(String.class)
                .timeout(STREAM_TIMEOUT)
                // 记录每一行原始响应，处理完一行后会释放内存，让内存稳定
                .doOnNext(line -> {
                    // 记录每一行原始响应，处理完一行后会释放内存，让内存稳定
                    log.debug("{}原始响应行: {}", config.name, line);
                })
                .doOnSubscribe(subscription -> {
                    log.info("{}流式请求开始 - URL: {}", config.name, config.url);
                })
                .doOnComplete(() -> {
                    log.info("{}流式请求完成", config.name);
                })
                // Predicate<String>lambda过滤器接收参数执行responseFilter返回boolean值
                .filter(line -> config.responseFilter.test(line))
                // Function<String, String>lambda转换器接收参数执行dataExtractor里的代码返回另一个值
                .map(line -> config.dataExtractor.apply(line))
                .mapNotNull(data -> {
                    // 决策理由：对于所有提供商，直接返回原始数据，避免不必要的解析和重构造
                    // 创建一个包含原始JSON数据的ChatCompletionChunk对象
                    ChatCompletionChunk chunk = new ChatCompletionChunk();
                    // 使用特殊标记来表示这是原始数据，在上层服务中可以识别并直接转发
                    chunk.setId("RAWDATA");
                    chunk.setObject(data); // 将原始JSON数据存储在object字段中

                    log.debug("{}直接转发原始数据: {}", config.name, data);
                    // 传进来的数据和想要返回数据不一样，一行的话会隐式返回，多行必须显式返回，返回map后的flux<ChatCompletionChunk>
                    return chunk;
                })
                .doOnError(error -> {
                    log.error("{}流式请求失败 - URL: {}, 错误: {}", config.name, config.url, error.getMessage(), error);
                });
    }

    /**
     * OpenAI兼容格式的非流式聊天完成方法
     * 根据不同的提供商配置相应的请求参数，统一使用OpenAI格式，直接返回原始JSON数据
     */
    private Mono<ChatCompletionResponse> openAiCompatibleChatCompletion(UserApiKey.ProviderType provider, String apiKey, ChatCompletionRequest request) {
        // 根据提供商获取配置信息
        ProviderConfig config = getProviderConfig(provider);

        // 构建请求体
        Map<String, Object> requestBody = buildOpenAiRequestBody(request, false, provider);

        // 决策理由：添加详细的请求日志来诊断不同提供商的问题
        log.info("=== {} 非流式请求详情 ===", config.name);
        log.info("请求URL: {}", config.url);
        log.info("请求头: Authorization=Bearer {}...", apiKey.substring(0, Math.min(10, apiKey.length())));
        if (config.additionalHeaders != null) {
            config.additionalHeaders.forEach((key, value) ->
                log.info("请求头: {}={}", key, value));
        }
        log.info("请求体: {}", requestBody);

        // 构建WebClient请求
        WebClient.RequestBodySpec requestSpec = webClient.post()
                .uri(config.url)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        // 添加提供商特定的请求头
        if (config.additionalHeaders != null) {
            config.additionalHeaders.forEach((key, value) -> requestSpec.header(key, value));
        }

        return requestSpec
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(REQUEST_TIMEOUT)
                .doOnSubscribe(subscription -> {
                    log.info("{}非流式请求开始 - URL: {}", config.name, config.url);
                })
                .doOnSuccess(response -> {
                    log.info("{}非流式请求完成", config.name);
                })
                .map(jsonStr -> {
                    // 决策理由：由于所有提供商都返回OpenAI格式，直接返回原始JSON数据，避免解析和重构造
                    // 创建一个包含原始JSON数据的ChatCompletionResponse对象
                    ChatCompletionResponse response = new ChatCompletionResponse();
                    // 使用特殊标记来表示这是原始数据，在上层服务中可以识别并直接转发
                    response.setId("RAWDATA");
                    response.setObject(jsonStr); // 将原始JSON数据存储在object字段中

                    log.debug("{}直接转发原始数据: {}", config.name, jsonStr);
                    return response;
                })
                .doOnError(error -> {
                    log.error("{}非流式请求失败 - URL: {}, 错误: {}", config.name, config.url, error.getMessage(), error);
                });
    }

    /**
     * OpenAI兼容格式的API密钥验证方法
     * 通过调用OpenAI兼容的/models端点来验证API密钥有效性，直接返回原始JSON数据
     */
    private Mono<ApiKeyValidationResult> validateOpenAiCompatibleApiKey(UserApiKey.ProviderType provider, String apiKey) {
        long startTime = System.currentTimeMillis();

        // 根据提供商获取配置信息
        ProviderConfig config = getProviderConfig(provider);

        // 构建模型列表请求URL
        String modelsUrl = config.url.replace("/chat/completions", "/models");

        log.info("=== {} API密钥验证详情 ===", config.name);
        log.info("验证URL: {}", modelsUrl);
        log.info("请求头: Authorization=Bearer {}...", apiKey.substring(0, Math.min(10, apiKey.length())));
        if (config.additionalHeaders != null) {
            config.additionalHeaders.forEach((key, value) ->
                log.info("请求头: {}={}", key, value));
        }

        // 构建WebClient请求
        WebClient.RequestHeadersSpec<?> requestSpec = webClient.get()
                .uri(modelsUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey);

        // 添加提供商特定的请求头
        if (config.additionalHeaders != null) {
            for (Map.Entry<String, String> header : config.additionalHeaders.entrySet()) {
                requestSpec = requestSpec.header(header.getKey(), header.getValue());
            }
        }

        return requestSpec
                .retrieve()
                .bodyToMono(String.class)
                .timeout(REQUEST_TIMEOUT)
                .doOnSubscribe(subscription -> {
                    log.info("{}API密钥验证开始 - URL: {}", config.name, modelsUrl);
                })
                .map(jsonResponse -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    log.info("{}API密钥验证成功 - 响应时间: {}ms", config.name, responseTime);
                    log.debug("{}API密钥验证响应: {}", config.name, jsonResponse);

                    // 决策理由：验证成功，返回包含原始JSON数据的验证结果
                    return new ApiKeyValidationResult(true, jsonResponse, responseTime);
                })
                .onErrorResume(error -> {
                    long responseTime = System.currentTimeMillis() - startTime;
                    String errorMessage = "API密钥验证失败: " + error.getMessage();
                    log.error("{}API密钥验证失败 - 响应时间: {}ms, 错误: {}", config.name, responseTime, error.getMessage());

                    return Mono.just(new ApiKeyValidationResult(false, errorMessage, responseTime));
                });
    }

    /**
     * OpenAI兼容格式的模型列表获取方法
     * 通过调用OpenAI兼容的/models端点获取模型列表，直接返回原始JSON数据包装的ModelInfo
     */
    private Mono<List<ModelInfo>> getOpenAiCompatibleModels(UserApiKey.ProviderType provider, String apiKey) {
        // 根据提供商获取配置信息
        ProviderConfig config = getProviderConfig(provider);

        // 构建模型列表请求URL
        String modelsUrl = config.url.replace("/chat/completions", "/models");

        log.info("=== {} 模型列表获取详情 ===", config.name);
        log.info("请求URL: {}", modelsUrl);
        log.info("请求头: Authorization=Bearer {}...", apiKey.substring(0, Math.min(10, apiKey.length())));
        if (config.additionalHeaders != null) {
            config.additionalHeaders.forEach((key, value) ->
                log.info("请求头: {}={}", key, value));
        }

        // 构建WebClient请求
        WebClient.RequestHeadersSpec<?> requestSpec = webClient.get()
                .uri(modelsUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey);

        // 添加提供商特定的请求头
        if (config.additionalHeaders != null) {
            for (Map.Entry<String, String> header : config.additionalHeaders.entrySet()) {
                requestSpec = requestSpec.header(header.getKey(), header.getValue());
            }
        }

        return requestSpec
                .retrieve()
                .bodyToMono(String.class)
                .timeout(REQUEST_TIMEOUT)
                .doOnSubscribe(subscription -> {
                    log.info("{}模型列表获取开始 - URL: {}", config.name, modelsUrl);
                })
                .doOnSuccess(response -> {
                    log.info("{}模型列表获取成功", config.name);
                })
                .map(jsonResponse -> {
                    // 决策理由：直接返回原始JSON数据，避免解析和重构造
                    // 创建一个特殊的ModelInfo对象来包装原始JSON数据
                    ModelInfo rawDataModel = new ModelInfo();
                    rawDataModel.setId("RAWDATA");
                    rawDataModel.setName(config.name + " Models Raw Data");
                    rawDataModel.setDescription(jsonResponse); // 将原始JSON数据存储在description字段中
                    rawDataModel.setProvider(config.name);
                    rawDataModel.setSupportsStreaming(true);
                    rawDataModel.setMaxContextLength(0);

                    log.debug("{}直接转发原始模型数据: {}", config.name, jsonResponse);
                    return List.of(rawDataModel);
                })
                .onErrorResume(error -> {
                    log.error("{}模型列表获取失败: {}", config.name, error.getMessage());
                    // 返回备用模型列表
                    return Mono.just(getFallbackModels(provider));
                });
    }

    /**
     * 获取备用模型列表
     */
    private List<ModelInfo> getFallbackModels(UserApiKey.ProviderType provider) {
        switch (provider) {
            case OPENAI:
                return Arrays.asList(
                        new ModelInfo("gpt-4", "GPT-4", 8192, "OpenAI GPT-4模型"),
                        new ModelInfo("gpt-3.5-turbo", "GPT-3.5 Turbo", 4096, "OpenAI GPT-3.5模型")
                );
            case ANTHROPIC:
                return getAnthropicFallbackModels();
            case GOOGLE:
                return getGoogleFallbackModels();
            default:
                return new ArrayList<>();
        }
    }

    /**
     * 提供商配置类
     */
    private static class ProviderConfig {
        final String name;
        final String url;
        final Map<String, String> additionalHeaders;
        final java.util.function.Predicate<String> responseFilter;
        final java.util.function.Function<String, String> dataExtractor;

        ProviderConfig(String name, String url, Map<String, String> additionalHeaders,
                      java.util.function.Predicate<String> responseFilter,
                      java.util.function.Function<String, String> dataExtractor) {
            this.name = name;
            this.url = url;
            this.additionalHeaders = additionalHeaders;
            this.responseFilter = responseFilter;
            this.dataExtractor = dataExtractor;
        }
    }

    /**
     * 根据提供商类型获取配置信息
     */
    private ProviderConfig getProviderConfig(UserApiKey.ProviderType provider) {
        switch (provider) {
            case OPENAI:
                AiConfig.Provider openaiProvider = aiConfig.getOpenAiProvider();
                String openaiBaseUrl = openaiProvider != null ? openaiProvider.getBaseUrl() : "https://api.openai.com/v1";
                return new ProviderConfig(
                    "OpenAI",
                    openaiBaseUrl + "/chat/completions",
                    null, // 无额外请求头
                    line -> line.startsWith("data: ") && !line.equals("data: [DONE]"), // 标准SSE格式过滤
                    line -> line.substring(6) // 移除 "data: " 前缀
                );

            case ANTHROPIC:
                AiConfig.Provider anthropicProvider = aiConfig.getAnthropicProvider();
                String anthropicBaseUrl = anthropicProvider != null ? anthropicProvider.getBaseUrl() : "https://api.anthropic.com/v1";
                Map<String, String> anthropicHeaders = new HashMap<>();
                anthropicHeaders.put("anthropic-version", "2023-06-01");
                return new ProviderConfig(
                    "Anthropic",
                    anthropicBaseUrl + "/chat/completions",
                    anthropicHeaders,
                    line -> {
                        // 决策理由：支持标准 SSE 格式和直接 JSON 格式
                        boolean isStandardSSE = line.startsWith("data: ") && !line.equals("data: [DONE]");
                        boolean isDirectJSON = !line.trim().isEmpty() &&
                                              !line.equals("[DONE]") &&
                                              line.trim().startsWith("{");
                        boolean isValidData = isStandardSSE || isDirectJSON;

                        if (!isValidData && !line.trim().isEmpty()) {
                            log.warn("过滤掉非数据行: {}", line);
                        }
                        return isValidData;
                    },
                    line -> {
                        // 智能提取数据：标准 SSE 格式移除前缀，直接 JSON 格式保持原样
                        if (line.startsWith("data: ")) {
                            return line.substring(6); // 移除 "data: " 前缀
                        } else {
                            return line; // 直接 JSON 格式
                        }
                    }
                );

            case GOOGLE:
                AiConfig.Provider googleProvider = aiConfig.getGoogleProvider();
                String googleBaseUrl = googleProvider != null ? googleProvider.getBaseUrl() : "https://generativelanguage.googleapis.com/v1beta/openai";
                return new ProviderConfig(
                    "Google",
                    googleBaseUrl + "/chat/completions",
                    null, // 无额外请求头
                    line -> {
                        // 决策理由：Google Gemini 直接返回 JSON，不是标准 SSE 格式
                        boolean isValidData = !line.trim().isEmpty() &&
                                             !line.equals("[DONE]") &&
                                             line.trim().startsWith("{");
                        if (!isValidData && !line.trim().isEmpty()) {
                            log.warn("过滤掉非数据行: {}", line);
                        }
                        return isValidData;
                    },
                    line -> line // Google Gemini 直接返回 JSON，无需移除前缀
                );

            default:
                throw new UnsupportedOperationException("不支持的提供商: " + provider);
        }
    }

    // ========================
    // OpenAI 适配实现
    // ========================









    // ========================
    // Anthropic 适配实现 (OpenAI 兼容格式)
    // ========================





    /**
     * 获取Anthropic备用模型列表
     */
    private List<ModelInfo> getAnthropicFallbackModels() {
        return Arrays.asList(
                new ModelInfo("claude-3-haiku", "Claude 3 Haiku", 200000, "快速响应，成本优化"),
                new ModelInfo("claude-3-sonnet", "Claude 3 Sonnet", 200000, "平衡性能和速度"),
                new ModelInfo("claude-3-opus", "Claude 3 Opus", 200000, "最强推理能力"),
                new ModelInfo("claude-3-5-sonnet", "Claude 3.5 Sonnet", 200000, "增强版 Sonnet"),
                new ModelInfo("claude-3-5-haiku", "Claude 3.5 Haiku", 200000, "增强版 Haiku"),
                new ModelInfo("claude-opus-4-20250514", "Claude Opus 4", 200000, "最新 Opus 4 模型"),
                new ModelInfo("claude-sonnet-4-20250514", "Claude Sonnet 4", 200000, "最新 Sonnet 4 模型")
        );
    }



    // ========================
    // Google AI 适配实现
    // ========================





    /**
     * 获取Google备用模型列表
     */
    private List<ModelInfo> getGoogleFallbackModels() {
        return Arrays.asList(
                new ModelInfo("gemini-pro", "Gemini Pro", 32768, "Google 通用大模型"),
                new ModelInfo("gemini-pro-vision", "Gemini Pro Vision", 32768, "支持图像理解的多模态模型"),
                new ModelInfo("gemini-1.5-pro", "Gemini 1.5 Pro", 1000000, "增强版 Pro，支持长上下文"),
                new ModelInfo("gemini-1.5-flash", "Gemini 1.5 Flash", 1000000, "快速响应版本"),
                new ModelInfo("gemini-2.0-flash-exp", "Gemini 2.0 Flash (Experimental)", 1000000, "最新实验版本")
        );
    }



    // ========================
    // 私有辅助方法
    // ========================

    /**
     * 构建OpenAI格式的请求体
     */
    private Map<String, Object> buildOpenAiRequestBody(ChatCompletionRequest request, boolean stream) {
        return buildOpenAiRequestBody(request, stream, null);
    }

    /**
     * 构建OpenAI格式的请求体（支持提供商特定参数）
     */
    private Map<String, Object> buildOpenAiRequestBody(ChatCompletionRequest request, boolean stream, UserApiKey.ProviderType provider) {
        Map<String, Object> body = new HashMap<>();
        body.put("model", request.getModel());
        body.put("messages", convertToOpenAiMessages(request.getMessages()));
        body.put("stream", stream);

        if (request.getTemperature() != null) {
            body.put("temperature", request.getTemperature());
        }
        if (request.getMaxTokens() != null) {
            body.put("max_tokens", request.getMaxTokens());
        }
        if (request.getTopP() != null) {
            body.put("top_p", request.getTopP());
        }

        // 决策理由：根据不同提供商添加推理参数
        if (Boolean.TRUE.equals(request.getIncludeReasoning()) && provider != null) {
            switch (provider) {
                case OPENAI:
                    // OpenAI o1 使用 reasoning_effort 参数（官方文档格式）
                    body.put("reasoning_effort", "medium"); // 可选值: low, medium, high
                    break;
                case ANTHROPIC:
                    // Claude 使用 thinking 配置（官方文档格式）
                    Map<String, Object> thinking = new HashMap<>();
                    thinking.put("type", "enabled");
                    thinking.put("budget_tokens", 10000); // 推理预算 token 数
                    body.put("thinking", thinking);
                    break;
                case GOOGLE:
                    // Gemini OpenAI 兼容 API 使用 reasoning_effort 参数
                    body.put("reasoning_effort", "medium");
                    break;
            }
            log.debug("为提供商 {} 添加推理参数", provider);
        }

        // 决策理由：处理Function Calling工具参数，根据不同提供商进行格式转换
        if (request.getTools() != null) {
            if (provider == UserApiKey.ProviderType.ANTHROPIC) {
                // Claude的OpenAI兼容API使用标准OpenAI格式（嵌套function结构）
                body.put("tools", convertFlatToolsToNestedForClaude(request.getTools()));
                log.debug("为Claude转换工具格式为嵌套结构");
            } else {
                // OpenAI和Gemini使用2025年最新扁平化格式
                body.put("tools", request.getTools());
                log.debug("为{}添加工具参数", provider);
            }
        }

        // 添加工具选择策略
        if (request.getToolChoice() != null) {
            body.put("tool_choice", request.getToolChoice());
        }

        // 决策理由：处理JSON输出格式，Claude需要特殊处理
        if (request.getResponseFormat() != null) {
            if (provider == UserApiKey.ProviderType.ANTHROPIC) {
                // Claude不支持response_format，通过修改消息添加JSON输出提示
                addJsonOutputPromptForClaude(body, request.getResponseFormat());
                log.debug("为Claude添加JSON输出提示词");
            } else {
                // OpenAI和Gemini支持response_format
                body.put("response_format", request.getResponseFormat());
                log.debug("为{}添加response_format参数", provider);
            }
        }

        // 决策理由：处理流式响应选项，Claude需要特殊处理
        if (request.getStreamOptions() != null) {
            if (provider == UserApiKey.ProviderType.ANTHROPIC) {
                // Claude不支持stream_options，但会在message_stop事件中自动包含usage
                log.debug("Claude自动在message_stop事件中包含usage信息，忽略stream_options参数");
            } else {
                // OpenAI和Gemini支持stream_options
                body.put("stream_options", request.getStreamOptions());
                log.debug("为{}添加stream_options参数", provider);
            }
        }

        return body;
    }

    /**
     * 转换为OpenAI消息格式（支持多模态）
     */
    private List<Map<String, Object>> convertToOpenAiMessages(List<ChatCompletionRequest.ChatMessage> messages) {
        List<Map<String, Object>> openAiMessages = new ArrayList<>();

        for (ChatCompletionRequest.ChatMessage message : messages) {
            Map<String, Object> openAiMessage = new HashMap<>();
            openAiMessage.put("role", message.getRole());

            // 支持多模态内容
            if (message.getContent() instanceof String) {
                // 纯文本消息
                openAiMessage.put("content", message.getContent());
            } else {
                // 多模态消息，直接传递content对象
                // 这样可以保持OpenAI标准的多模态格式
                openAiMessage.put("content", message.getContent());
            }

            openAiMessages.add(openAiMessage);
        }

        return openAiMessages;
    }

    /**
     * 解析OpenAI流式响应块
     */
    private ChatCompletionChunk parseOpenAiStreamChunk(String jsonStr) {
        try {
            // 决策理由：添加输入验证以避免解析空或无效数据
            if (jsonStr == null || jsonStr.trim().isEmpty()) {
                log.warn("收到空的JSON字符串，跳过解析");
                return null;
            }

            JsonNode node = objectMapper.readTree(jsonStr);
            ChatCompletionChunk chunk = new ChatCompletionChunk();

            // 决策理由：添加字段存在性检查以提高解析的健壮性
            String id = node.path("id").asText(null);
            String object = node.path("object").asText(null);
            long created = node.path("created").asLong(0);
            String model = node.path("model").asText(null);

            chunk.setId(id);
            chunk.setObject(object);
            chunk.setCreated(created);
            chunk.setModel(model);

            // 解析choices
            JsonNode choicesNode = node.path("choices");
            if (choicesNode.isArray() && choicesNode.size() > 0) {
                JsonNode choice = choicesNode.get(0);
                ChatCompletionChunk.Choice chunkChoice = new ChatCompletionChunk.Choice();
                chunkChoice.setIndex(choice.path("index").asInt(0));

                String finishReason = choice.path("finish_reason").asText(null);
                chunkChoice.setFinishReason(finishReason);

                // 决策理由：记录finish_reason以便调试流式传输结束的原因
                if (finishReason != null && !"null".equals(finishReason)) {
                    log.info("检测到流式响应结束标志 - finish_reason: {}, chunk_id: {}", finishReason, id);
                }

                JsonNode delta = choice.path("delta");
                if (!delta.isMissingNode()) {
                    ChatCompletionChunk.Delta chunkDelta = new ChatCompletionChunk.Delta();
                    String content = delta.path("content").asText(null);
                    chunkDelta.setContent(content);
                    chunkChoice.setDelta(chunkDelta);

                    // 决策理由：记录内容长度以便监控数据传输
                    if (content != null && !"null".equals(content)) {
                        log.debug("解析到内容片段 - 长度: {}, 内容: {}", content.length(),
                                content.length() > 50 ? content.substring(0, 50) + "..." : content);
                    }
                }

                chunk.setChoices(List.of(chunkChoice));
            } else {
                log.debug("响应块中没有choices数据: {}", jsonStr);
            }

            return chunk;
        } catch (Exception e) {
            log.error("解析OpenAI流式响应失败 - JSON: {}, 错误: {}", jsonStr, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析OpenAI完整响应
     */
    private ChatCompletionResponse parseOpenAiResponse(String jsonStr) {
        try {
            // 转为json树型结构(因为每个AI大模型返回的数据有可能轻微不同，所以用JsonNode灵活存放，复杂结构也用JsonNode)
            JsonNode node = objectMapper.readTree(jsonStr);
            ChatCompletionResponse response = new ChatCompletionResponse();

            response.setId(node.path("id").asText());
            response.setObject(node.path("object").asText());
            response.setCreated(node.path("created").asLong());
            response.setModel(node.path("model").asText());

            // 解析choices
            JsonNode choicesNode = node.path("choices");
            if (choicesNode.isArray() && choicesNode.size() > 0) {
                JsonNode choice = choicesNode.get(0);
                ChatCompletionResponse.Choice responseChoice = new ChatCompletionResponse.Choice();
                responseChoice.setIndex(choice.path("index").asInt());
                responseChoice.setFinishReason(choice.path("finish_reason").asText());

                JsonNode message = choice.path("message");
                if (!message.isMissingNode()) {
                    ChatCompletionRequest.ChatMessage chatMessage = new ChatCompletionRequest.ChatMessage();
                    chatMessage.setRole(message.path("role").asText());
                    chatMessage.setContent(message.path("content").asText());
                    responseChoice.setMessage(chatMessage);
                }

                response.setChoices(List.of(responseChoice));
            }

            // 解析usage
            JsonNode usageNode = node.path("usage");
            if (!usageNode.isMissingNode()) {
                ChatCompletionResponse.Usage usage = new ChatCompletionResponse.Usage();
                usage.setPromptTokens(usageNode.path("prompt_tokens").asInt());
                usage.setCompletionTokens(usageNode.path("completion_tokens").asInt());
                usage.setTotalTokens(usageNode.path("total_tokens").asInt());
                response.setUsage(usage);
            }

            return response;
        } catch (Exception e) {
            log.error("解析OpenAI响应失败: {}", jsonStr, e);
            return null;
        }
    }

    /**
     * 解析OpenAI模型列表
     */
    private List<ModelInfo> parseOpenAiModels(String jsonStr) {
        try {
            JsonNode node = objectMapper.readTree(jsonStr);
            List<ModelInfo> models = new ArrayList<>();

            JsonNode dataNode = node.path("data");
            if (dataNode.isArray()) {
                for (JsonNode modelNode : dataNode) {
                    String id = modelNode.path("id").asText();
                    if (id.contains("gpt")) { // 只返回GPT模型
                        models.add(new ModelInfo(id, id.toUpperCase(), 4096, "OpenAI GPT模型"));
                    }
                }
            }

            return models;
        } catch (Exception e) {
            log.error("解析OpenAI模型列表失败: {}", jsonStr, e);
            return new ArrayList<>();
        }
    }

    // ========================
    // 图片生成适配实现
    // ========================

    /**
     * OpenAI图片生成
     */
    private Mono<OpenAiImageResponse> generateOpenAiImage(String apiKey, OpenAiImageRequest request) {
        AiConfig.Provider openaiProvider = aiConfig.getOpenAiProvider();
        String baseUrl = openaiProvider != null ? openaiProvider.getBaseUrl() : "https://api.openai.com/v1";
        String url = baseUrl + "/images/generations";

        // 构建OpenAI格式的请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", request.getModel());
        requestBody.put("prompt", request.getPrompt());
        requestBody.put("n", request.getN());
        requestBody.put("size", request.getSize());
        requestBody.put("quality", request.getQuality());
        requestBody.put("style", request.getStyle());
        requestBody.put("response_format", request.getResponseFormat());
        if (request.getUser() != null) {
            requestBody.put("user", request.getUser());
        }

        return webClient.post()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMinutes(2)) // 图片生成可能需要更长时间
                .doOnSubscribe(subscription -> {
                    log.info("OpenAI图片生成请求开始 - URL: {}, 模型: {}", url, request.getModel());
                })
                .doOnSuccess(response -> {
                    log.info("OpenAI图片生成请求完成");
                })
                .map(response -> this.parseOpenAiImageResponse(response))
                .onErrorMap(throwable -> {
                    log.error("OpenAI图片生成失败", throwable);
                    return new RuntimeException("OpenAI图片生成失败: " + throwable.getMessage());
                });
    }

    /**
     * Gemini图片生成（使用Imagen模型）
     */
    private Mono<OpenAiImageResponse> generateGeminiImage(String apiKey, OpenAiImageRequest request) {
        AiConfig.Provider googleProvider = aiConfig.getGoogleProvider();
        String baseUrl = googleProvider != null ? googleProvider.getBaseUrl() : "https://generativelanguage.googleapis.com/v1beta";

        // Gemini使用不同的API端点和格式
        String url = baseUrl + "/models/imagen-3.0-generate-001:generateImage";

        // 构建Gemini格式的请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("prompt", request.getPrompt());

        // 映射尺寸格式
        String geminiSize = mapSizeToGemini(request.getSize());
        if (geminiSize != null) {
            requestBody.put("aspectRatio", geminiSize);
        }

        return webClient.post()
                .uri(url + "?key=" + apiKey)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMinutes(2))
                .doOnSubscribe(subscription -> {
                    log.info("Gemini图片生成请求开始 - URL: {}", url);
                })
                .doOnSuccess(response -> {
                    log.info("Gemini图片生成请求完成");
                })
                .map(response -> this.parseGeminiImageResponse(response))
                .onErrorMap(throwable -> {
                    log.error("Gemini图片生成失败", throwable);
                    return new RuntimeException("Gemini图片生成失败: " + throwable.getMessage());
                });
    }

    /**
     * 解析OpenAI图片生成响应
     */
    private OpenAiImageResponse parseOpenAiImageResponse(String jsonStr) {
        try {
            JsonNode node = objectMapper.readTree(jsonStr);

            Long created = node.path("created").asLong();
            List<OpenAiImageResponse.ImageData> imageDataList = new ArrayList<>();

            JsonNode dataNode = node.path("data");
            if (dataNode.isArray()) {
                for (JsonNode imageNode : dataNode) {
                    OpenAiImageResponse.ImageData.ImageDataBuilder builder =
                        OpenAiImageResponse.ImageData.builder();

                    if (imageNode.has("url")) {
                        builder.url(imageNode.path("url").asText());
                    }
                    if (imageNode.has("b64_json")) {
                        builder.b64Json(imageNode.path("b64_json").asText());
                    }
                    if (imageNode.has("revised_prompt")) {
                        builder.revisedPrompt(imageNode.path("revised_prompt").asText());
                    }

                    imageDataList.add(builder.build());
                }
            }

            return OpenAiImageResponse.builder()
                    .created(created)
                    .data(imageDataList)
                    .build();
        } catch (Exception e) {
            log.error("解析OpenAI图片响应失败: {}", jsonStr, e);
            throw new RuntimeException("解析图片响应失败: " + e.getMessage());
        }
    }

    /**
     * 解析Gemini图片生成响应
     */
    private OpenAiImageResponse parseGeminiImageResponse(String jsonStr) {
        try {
            JsonNode node = objectMapper.readTree(jsonStr);

            List<OpenAiImageResponse.ImageData> imageDataList = new ArrayList<>();

            // Gemini响应格式可能不同，需要适配
            if (node.has("candidates")) {
                JsonNode candidates = node.path("candidates");
                if (candidates.isArray() && candidates.size() > 0) {
                    JsonNode firstCandidate = candidates.get(0);
                    if (firstCandidate.has("content")) {
                        JsonNode content = firstCandidate.path("content");
                        if (content.has("parts")) {
                            JsonNode parts = content.path("parts");
                            if (parts.isArray() && parts.size() > 0) {
                                JsonNode part = parts.get(0);
                                if (part.has("inlineData")) {
                                    JsonNode inlineData = part.path("inlineData");
                                    String base64Data = inlineData.path("data").asText();

                                    OpenAiImageResponse.ImageData imageData =
                                        OpenAiImageResponse.ImageData.builder()
                                            .b64Json(base64Data)
                                            .build();
                                    imageDataList.add(imageData);
                                }
                            }
                        }
                    }
                }
            }

            return OpenAiImageResponse.builder()
                    .created(System.currentTimeMillis() / 1000)
                    .data(imageDataList)
                    .build();
        } catch (Exception e) {
            log.error("解析Gemini图片响应失败: {}", jsonStr, e);
            throw new RuntimeException("解析Gemini图片响应失败: " + e.getMessage());
        }
    }

    /**
     * 映射尺寸格式到Gemini格式
     */
    private String mapSizeToGemini(String openaiSize) {
        switch (openaiSize) {
            case "1024x1024":
                return "1:1";
            case "1024x1792":
                return "9:16";
            case "1792x1024":
                return "16:9";
            default:
                return "1:1"; // 默认正方形
        }
    }

    /**
     * 将扁平化的OpenAI工具格式转换为Claude期望的嵌套格式
     * <p>
     * 根据Anthropic OpenAI兼容文档，Claude期望标准OpenAI格式（嵌套function结构）：
     * - 输入: tools[].name, tools[].description, tools[].parameters (2025年扁平化格式)
     * - 输出: tools[].type, tools[].function.name, tools[].function.description, tools[].function.parameters
     * </p>
     */
    @SuppressWarnings("unchecked")
    private Object convertFlatToolsToNestedForClaude(Object tools) {
        try {
            if (tools instanceof List) {
                List<Object> toolsList = (List<Object>) tools;
                List<Map<String, Object>> claudeTools = new ArrayList<>();

                for (Object tool : toolsList) {
                    if (tool instanceof Map) {
                        Map<String, Object> toolMap = (Map<String, Object>) tool;

                        if ("function".equals(toolMap.get("type"))) {
                            Map<String, Object> claudeTool = new HashMap<>();
                            Map<String, Object> functionObj = new HashMap<>();

                            // 将扁平化属性转换为嵌套结构
                            claudeTool.put("type", "function");
                            functionObj.put("name", toolMap.get("name"));
                            functionObj.put("description", toolMap.get("description"));
                            functionObj.put("parameters", toolMap.get("parameters"));
                            claudeTool.put("function", functionObj);

                            claudeTools.add(claudeTool);
                        }
                    }
                }

                return claudeTools;
            }
        } catch (Exception e) {
            log.error("转换Claude工具格式失败", e);
        }

        // 如果转换失败，返回原始工具
        return tools;
    }

    /**
     * 为Claude添加JSON输出提示词
     * <p>
     * 由于Claude不支持response_format参数，需要通过修改消息来实现JSON输出
     * </p>
     */
    @SuppressWarnings("unchecked")
    private void addJsonOutputPromptForClaude(Map<String, Object> body, Object responseFormat) {
        try {
            if (responseFormat instanceof Map) {
                Map<String, Object> formatMap = (Map<String, Object>) responseFormat;
                String type = (String) formatMap.get("type");

                if ("json_object".equals(type)) {
                    // 获取消息列表
                    Object messagesObj = body.get("messages");
                    if (messagesObj instanceof List) {
                        List<Map<String, Object>> messages = (List<Map<String, Object>>) messagesObj;

                        // 在最后一条用户消息后添加JSON输出要求
                        for (int i = messages.size() - 1; i >= 0; i--) {
                            Map<String, Object> message = messages.get(i);
                            if ("user".equals(message.get("role"))) {
                                Object content = message.get("content");
                                if (content instanceof String) {
                                    String originalContent = (String) content;
                                    String jsonPrompt = originalContent + "\n\n请以有效的JSON格式返回响应，不要包含任何其他文本或解释。";
                                    message.put("content", jsonPrompt);
                                } else if (content instanceof List) {
                                    // 多模态内容，在最后添加文本部分
                                    List<Map<String, Object>> contentParts = (List<Map<String, Object>>) content;
                                    Map<String, Object> jsonPromptPart = new HashMap<>();
                                    jsonPromptPart.put("type", "text");
                                    jsonPromptPart.put("text", "\n\n请以有效的JSON格式返回响应，不要包含任何其他文本或解释。");
                                    contentParts.add(jsonPromptPart);
                                }
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("为Claude添加JSON输出提示词失败", e);
        }
    }
}
